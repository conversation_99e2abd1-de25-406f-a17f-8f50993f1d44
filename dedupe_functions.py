import re
import sys
from collections import defaultdict

INPUT_FILE = "Trione_Backend.py"
OUTPUT_FILE = INPUT_FILE  # Overwrite the original file

DEF_PATTERN = re.compile(r"^def ([a-zA-Z_][a-zA-Z0-9_]*)\s*\(")

# Step 1: Find all function definitions and their line ranges
with open(INPUT_FILE, encoding="utf-8") as f:
    lines = f.readlines()

defs = []  # (func_name, start_line, end_line)
current_func = None
current_indent = None
for i, line in enumerate(lines):
    match = DEF_PATTERN.match(line.lstrip())
    if match:
        if current_func is not None:
            # Previous function ends at the line before this one
            defs[-1] = (defs[-1][0], defs[-1][1], i-1)
        func_name = match.group(1)
        indent = len(line) - len(line.lstrip())
        defs.append([func_name, i, None, indent])
        current_func = func_name
        current_indent = indent
    elif current_func is not None:
        # Check if this line is less indented, meaning function ended
        if line.strip() == '':
            continue
        indent = len(line) - len(line.lstrip())
        if indent <= current_indent and not line.lstrip().startswith('#'):
            # Function ended
            defs[-1] = (defs[-1][0], defs[-1][1], i-1)
            current_func = None
            current_indent = None
# If last function goes to end of file
if defs and defs[-1][2] is None:
    defs[-1] = (defs[-1][0], defs[-1][1], len(lines)-1)

defs_by_name = defaultdict(list)
for func_name, start, end, *_ in defs:
    defs_by_name[func_name].append((start, end))

# Step 2: For duplicates, keep only the largest
to_remove = set()
for func_name, ranges in defs_by_name.items():
    if len(ranges) > 1:
        # Compare sizes
        sizes = [(end-start+1, start, end) for start, end in ranges]
        sizes.sort(reverse=True)  # Largest first
        # Keep the largest, remove the rest
        for _, start, end in sizes[1:]:
            to_remove.update(range(start, end+1))

# Step 3: Write new file without removed lines
with open(OUTPUT_FILE, 'w', encoding="utf-8") as f:
    for i, line in enumerate(lines):
        if i not in to_remove:
            f.write(line)

print(f"Removed {len(to_remove)} lines from duplicate functions. Overwrote {OUTPUT_FILE}.") 