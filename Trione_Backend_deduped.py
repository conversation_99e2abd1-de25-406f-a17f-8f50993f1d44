###############################################################################
#                                                                             #
#                         TRIONE BATH CORPORATION ERP                         #
#                                                                             #
#                              BACKEND SERVER                                 #
#                                                                             #
###############################################################################

'''
This is the main backend server for the Trione Bath Corporation ERP system.
It handles all API requests, database operations, and serves the frontend files.

The server is built using Flask and provides a RESTful API for the frontend
to interact with the database. It also uses SocketIO for real-time updates.

Author: Harsh Nandha
Version: 1.4.6
Last Updated: May 2025
'''

#==============================================================================
#                             IMPORTS & SETUP
#==============================================================================
# Import everything we need from our centralized imports file
# This keeps our code clean and makes dependencies easy to manage

from imports import *
import time  # Added for timestamp-based fallback order IDs
from flask import abort, render_template, send_from_directory, g  # Added for manufacturing module and favicon
from werkzeug.utils import secure_filename  # Added for file upload handling

# Import manufacturing routes - needed for the manufacturing module
try:
    from manufacturing_routes import (
        serve_casting_order_page, serve_cnc_outward_page,
        serve_primary_store_receipt_page, serve_buffing_outward_page,
        serve_plating_outward_page, serve_assembly_receipt_page,
        register_manufacturing_blueprint 
    )
except ImportError:
    # Fallback if manufacturing_routes.py is not available
    print("Warning: manufacturing_routes.py not found - some manufacturing functions may not work")
    serve_casting_order_page = serve_cnc_outward_page = serve_primary_store_receipt_page = serve_buffing_outward_page = serve_plating_outward_page = serve_assembly_receipt_page = lambda: "Feature not available"
    register_manufacturing_blueprint = lambda app: print("Warning: Manufacturing blueprint not registered") # Add fallback

# Import purchase order routes - needed for the purchase order module
try:
    from purchase_order_routes import register_purchase_order_blueprint
except ImportError:
    # Fallback if purchase_order_routes.py is not available
    print("Warning: purchase_order_routes.py not found - purchase order functions may not work")
    register_purchase_order_blueprint = lambda app: print("Warning: Purchase Order blueprint not registered")

# Import report integration - needed for the reporting module
try:
    from report_integration import integrate_reports
except ImportError:
    # Fallback if report_integration.py is not available
    print("Warning: report_integration.py not found - reporting functions may not work")
    integrate_reports = lambda app: print("Warning: Reports integration not registered")

# Import WhatsApp service blueprint - needed for WhatsApp messaging
try:
    from whatsapp_service import register_whatsapp_service_blueprint
except ImportError:
    # Fallback if whatsapp_service.py is not available
    print("Warning: whatsapp_service.py not found - WhatsApp messaging functions may not work")
    register_whatsapp_service_blueprint = lambda app: print("Warning: WhatsApp service blueprint not registered")

# Set up logging so we can track what's happening in our application
# This is super helpful for debugging and monitoring the app's health
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.DEBUG  # Changed from logging.INFO to logging.DEBUG
)
logger = logging.getLogger(__name__)
logger.info("Initializing Trione Bath Corporation ERP Backend...")

#==============================================================================
#                         APPLICATION INITIALIZATION
#==============================================================================
# Create our Flask application - this is the heart of our backend
# The static_folder parameter tells Flask where to find static assets
# The template_folder parameter tells Flask where to find HTML templates
app = Flask(__name__, static_folder="static", template_folder=".")

# Configure template loader to look in multiple directories
from jinja2 import FileSystemLoader, ChoiceLoader
app.jinja_loader = ChoiceLoader([
    FileSystemLoader('.'),  # The default loader
    FileSystemLoader('FrontEnd'),  # Add FrontEnd directory for manufacturing templates
    FileSystemLoader('templates')  # Add templates directory for purchase order templates
])

# Enable Cross-Origin Resource Sharing (CORS)
# This allows our frontend to make requests to our backend even if they're on different domains
# It's essential for modern web applications with separate frontend and backend
CORS(app, resources={r"/*": {"origins": "*", "headers": ["Content-Type", "Authorization"]}})
logger.info("CORS configured for all origins")

# Set up SocketIO for real-time communication
# This lets us push updates to the frontend without the frontend having to poll for changes
# We're using eventlet as our async mode for better performance
socketio = SocketIO(app, async_mode='eventlet')
logger.info("SocketIO initialized with eventlet async mode")

# Add global keyboard shortcut script to all HTML responses
@app.after_request
import os
from datetime import datetime
import json

# Request logging middleware
@app.before_request
@app.after_request
DATABASE_PATH = 'Trione_ERP_Database.db'
logger.info(f"Database path set to: {DATABASE_PATH}")

@app.route('/favicon.ico')
@app.route('/manufacturing')
@app.route('/manufacturing/dashboard')
@app.route('/manufacturing/scrap_entry')
@app.route('/manufacturing/api/scrap_entries', methods=['GET'])
@app.route('/manufacturing/api/scrap_entries', methods=['POST'])
@app.route('/manufacturing/casting')
@app.route('/manufacturing/casting_order')
@app.route('/manufacturing/api/casting', methods=['GET'])
@app.route('/manufacturing/api/casting', methods=['POST'])
@app.route('/manufacturing/cnc')
@app.route('/manufacturing/api/cnc', methods=['GET'])
@app.route('/manufacturing/api/cnc', methods=['POST'])
@app.route('/manufacturing/cnc_outward')
@app.route('/manufacturing/primary_store')
@app.route('/manufacturing/api/primary_store', methods=['GET'])
@app.route('/manufacturing/api/primary_store', methods=['POST'])
@app.route('/manufacturing/primary_store_receipt')
@app.route('/manufacturing/buffing')
@app.route('/manufacturing/api/buffing', methods=['GET'])
@app.route('/manufacturing/api/buffing', methods=['POST'])
@app.route('/manufacturing/buffing_outward')
@app.route('/manufacturing/final_store')
@app.route('/manufacturing/api/final_store', methods=['GET'])
@app.route('/manufacturing/api/final_store', methods=['POST'])
@app.route('/manufacturing/plating_outward')
@app.route('/manufacturing/assembly_receipt')
@app.route('/manufacturing/products')
@app.route('/manufacturing/api/products', methods=['GET'])
@app.route('/manufacturing/api/products', methods=['POST'])
@app.route('/manufacturing/api/products/<product_code>', methods=['PUT'])
@app.route('/manufacturing/api/products/<product_code>', methods=['DELETE'])
@app.route('/manufacturing/reports')
@app.route('/manufacturing/api/reports/material_loss', methods=['GET'])
@app.route('/manufacturing/api/reports/production_history/<product_code>', methods=['GET'])
@app.route('/')
@app.route('/main_dashboard')
@app.route('/accounts')
@app.route('/tablet')
@app.route('/order_book')
@app.route('/client_portal')
@app.route('/product_names')
@app.route('/packing')
@app.route('/management')
@app.route('/clientnoteadd')
@app.route('/database_editor')
@app.route('/test')
@app.route('/notes')
@app.route('/merge_orders_page')
@app.route('/create_pending_order_page')
@app.route('/frontend/<path:filename>')
@app.route('/static/<path:filename>')
@app.route('/get_client_data', methods=['GET'])
@app.route('/client_orders/<client_id>', methods=['GET'])
@app.route('/api/clients', methods=['GET'])
@app.route('/today_orders_by_client/<client_id>')
@app.route('/api/product-names', methods=['GET'])
@app.route('/api/product-name/<product_code>', methods=['GET'])
@app.route('/api/product-names', methods=['POST'])
@app.route('/orders/<timeframe>')
@app.route('/merge_orders', methods=['POST'])
@app.route('/create_pending_order', methods=['POST'])
def create_pending_order():
    """Create a new pending order based on remaining products from an existing order.

    This endpoint creates a new order with the remaining (unpacked) products from
    an original order. It's useful for creating follow-up orders for incomplete orders.

    Request body should contain:
    - originalOrderId: The ID of the original order to copy remaining products from
    - clientId: The client ID for the new order

    Returns:
        JSON: Success message with new order details or error message
    """
    logger.info("API request received: create_pending_order")

    conn = get_db_connection()
    c = conn.cursor()

    try:
        # Retrieve the order data from the request payload
        order_data = request.get_json()

        # Extract required parameters
        original_order_id = order_data.get('originalOrderId')
        client_id = order_data.get('clientId')

        # Validate input data
        if not client_id:
            logger.warning("No client ID provided in create_pending_order request")
            return jsonify({
                'status': 'error',
                'message': 'Client ID is required'
            }), 400

        if not original_order_id:
            logger.warning("No original order ID provided in create_pending_order request")
            return jsonify({
                'status': 'error',
                'message': 'Original order ID is required'
            }), 400

        # Get the next order ID
        c.execute("SELECT COUNT(*) FROM order_counter WHERE id = 1")
        count = c.fetchone()[0]

        if count == 0:
            # No record exists, insert one
            logger.info("No order_counter record found, creating initial record")
            c.execute("INSERT INTO order_counter (id, last_order_id) VALUES (1, 1000)")
            new_order_id = 1000
        else:
            # Update existing record
            c.execute("UPDATE order_counter SET last_order_id = last_order_id + 1 WHERE id = 1")
            c.execute("SELECT last_order_id FROM order_counter WHERE id = 1")
            result = c.fetchone()
            if result is None:
                # Fallback if query returns None
                logger.warning("Failed to get last_order_id, using fallback value")
                new_order_id = int(time.time()) % 10000  # Use timestamp as fallback
            else:
                new_order_id = result[0]

        logger.info(f"Generated new order ID: {new_order_id}")

        # Fetch remaining products from the original order
        remaining_products = []
        original_order_date = None

        # Get the original order date
        c.execute("SELECT order_date FROM orders WHERE order_id = ?", (original_order_id,))
        result = c.fetchone()
        if result:
            original_order_date = result[0]  # The original order's date
        else:
            logger.warning(f"Original order {original_order_id} not found")
            return jsonify({
                'status': 'error',
                'message': f'Original order {original_order_id} not found'
            }), 404

        # Fetch remaining products (unpacked quantities)
        c.execute('''
            SELECT product_code,
                   quantity_pcs - packed_pcs as remaining_pcs,
                   quantity_sets - packed_sets as remaining_sets
            FROM order_details
            WHERE order_id = ? AND
                  ((quantity_pcs - packed_pcs) > 0 OR (quantity_sets - packed_sets) > 0)
        ''', (original_order_id,))
        db_products = c.fetchall()

        # Convert database tuples to dictionaries matching the expected format
        remaining_products = [
            {
                'productCode': product[0],
                'quantityPcs': product[1],
                'quantitySets': product[2],
                # For backward compatibility
                'quantityToMake': product[1] + (product[2] * 2)  # 1 SET = 2 PCS
            } for product in db_products if product[1] > 0 or product[2] > 0
        ]

        if not remaining_products:
            logger.warning(f"No remaining products found in order {original_order_id}")
            return jsonify({
                'status': 'error',
                'message': f'No remaining products found in order {original_order_id}'
            }), 400

        # Insert the new order
        current_date = datetime.now().strftime('%Y-%m-%d')
        scheduled_date = (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d")
        status = f'Pending of {original_order_id}'

        c.execute('''
            INSERT INTO orders
            (order_id, client_id, order_date, status, scheduled_date, is_today)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (new_order_id, client_id, current_date, status, scheduled_date, 0))

        # Insert order details
        for product in remaining_products:
            c.execute('''
                INSERT INTO order_details
                (order_id, product_code, quantity_pcs, quantity_sets, packed_pcs, packed_sets)
                VALUES (?, ?, ?, ?, 0, 0)
            ''', (new_order_id, product['productCode'], product['quantityPcs'], product['quantitySets']))

        # Commit the transaction
        conn.commit()

        # Generate the PO
        try:
            # Call the generate_po function directly
            response = generate_po_for_pending_order(new_order_id, client_id, remaining_products, original_order_date)
            logger.info(f"PO generated for pending order {new_order_id}")
        except Exception as po_error:
            logger.error(f"Error generating PO for pending order {new_order_id}: {str(po_error)}")
            # Log error but don't interrupt order creation

        # Prepare response
        return jsonify({
            'status': 'success',
            'message': 'Pending order created successfully',
            'orderId': new_order_id,
            'originalOrderId': original_order_id,
            'clientId': client_id,
            'orderDate': current_date,
            'scheduledDate': scheduled_date,
            'totalProducts': len(remaining_products),
            'totalQuantityPcs': sum(p.get('quantityPcs', 0) for p in remaining_products),
            'totalQuantitySets': sum(p.get('quantitySets', 0) for p in remaining_products)
        }), 200

    except sqlite3.Error as db_error:
        conn.rollback()
        logger.error(f"Database error in create_pending_order: {str(db_error)}")
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(db_error)}"
        }), 500

    except Exception as e:
        conn.rollback()
        logger.error(f"Error creating pending order: {str(e)}", exc_info=True)
        return jsonify({
            'status': 'error',
            'message': f'An unexpected error occurred: {str(e)}'
        }), 500

    finally:
        conn.close()


def generate_po_for_pending_order(order_id, client_id, products, original_order_date=None):
    """Generate a purchase order for a pending order.

    This function is similar to the generate_po endpoint but designed to be called
    directly from the create_pending_order function.

    Args:
        order_id (int): The ID of the new pending order
        client_id (str): The client ID
        products (list): List of product dictionaries with quantities
        original_order_date (str, optional): The date of the original order

    Returns:
        dict: Success message and file path
    """
    logger.info(f"Generating PO for pending order {order_id} from original order date {original_order_date}")

    # Load the Excel template
    template_paths = [
        'D:/COMMON DATA NEW/Handle ERP/Order PO/PO TEMPLATE.xlsx',
        'D:/ORDERS/PO TEMPLATE.xlsx',
        'PO TEMPLATE.xlsx'
    ]

    template_path = None
    for path in template_paths:
        if os.path.exists(path):
            template_path = path
            logger.info(f"Found PO template at: {path}")
            break

    if not template_path:
        logger.error("PO template not found in any of the expected locations")
        raise FileNotFoundError("PO template file not found")

    # Load the workbook and select the active worksheet
    workbook = load_workbook(template_path)
    worksheet = workbook.active

    # Initialize variables with default values
    transport = "Transport : "
    booking = "Booking : "
    logo_path = ""
    party_name_value = ''

    try:
        # Use get_all_clients() to fetch all clients and find the matching one
        try:
            from flask import current_app
            with current_app.app_context():
                all_clients_response = get_all_clients()
                if hasattr(all_clients_response, 'get_json'):
                    all_clients_data = all_clients_response.get_json()
                    if all_clients_data and 'data' in all_clients_data:
                        for client in all_clients_data['data']:
                            # Try both 'clientId' and 'ClientId' for robustness
                            if str(client.get('clientId', client.get('ClientId', ''))) == str(client_id):
                                party_name_value = client.get('Party_Name') or client.get('partyName') or ''
                                break
        except Exception as e:
            logger.warning(f"Could not fetch party name from get_all_clients: {str(e)}")

        if os.path.exists('client_db.db'):
            client_conn = sqlite3.connect('client_db.db')
            client_cursor = client_conn.cursor()

            client_cursor.execute("PRAGMA table_info(clients)")
            columns = [column[1] for column in client_cursor.fetchall()]

            if 'client_Id' in columns:
                query_columns = []
                if 'Transport' in columns:
                    query_columns.append('Transport')
                else:
                    query_columns.append("'' AS Transport")

                if 'Booking' in columns:
                    query_columns.append('Booking')
                else:
                    query_columns.append("'' AS Booking")

                if 'Logo' in columns:
                    query_columns.append('Logo')
                else:
                    query_columns.append("'' AS Logo")

                query = f"SELECT {', '.join(query_columns)} FROM clients WHERE client_Id = ?"
                client_cursor.execute(query, (client_id,))
                client_info = client_cursor.fetchone()

            client_conn.close()

        if client_info:
            transport = f"Transport : {client_info[0] or ''}"
            booking = f"Booking : {client_info[1] or ''}"
            logo_path = client_info[2] if len(client_info) > 2 else ""

        logger.info(f"Retrieved client info for {client_id}: Transport={transport}, Booking={booking}, MARKO={client_id}, PartyName={party_name_value}")

    except Exception as e:
        logger.error(f"Error retrieving client information: {str(e)}")
        # Continue without client info

    # Set header information in the worksheet
    worksheet['A1'] = f"Transport : {transport}"
    worksheet['D1'] = f"Booking : {booking}"
    worksheet['H1'] = f"Order ID : {order_id}"

    # Set client ID and dates
    worksheet['C2'] = f"Client ID : {client_id}"
    worksheet['H2'] = f"Order Date : {datetime.now().strftime('%d-%m-%Y')}"

    # Set party name (client ID) and scheduled date
    worksheet['A3'] = f"Party Name : {party_name_value}"

    # Get scheduled date from database
    conn = get_db_connection()
    c = conn.cursor()
    c.execute("SELECT scheduled_date FROM orders WHERE order_id = ?", (order_id,))
    scheduled_date_result = c.fetchone()
    conn.close()

    scheduled_date = 'Not Scheduled'
    if scheduled_date_result and scheduled_date_result[0]:
        try:
            date_obj = datetime.strptime(scheduled_date_result[0], '%Y-%m-%d')
            scheduled_date = date_obj.strftime('%d-%m-%Y')
        except ValueError:
            pass

    worksheet['H3'] = f"Scheduled Date : {scheduled_date}"

    # Add reference to original order if available
    if original_order_date:
        try:
            date_obj = datetime.strptime(original_order_date, '%Y-%m-%d')
            formatted_date = date_obj.strftime('%d-%m-%Y')
            worksheet['B4'] = f"Pending from order {order_id} dated {formatted_date}"
        except ValueError:
            worksheet['B4'] = f"Pending from order {order_id}"

    # Calculate total quantity
    total_quantity = sum(p.get('quantity_pcs', 0) for p in products)
    worksheet['I4'] = total_quantity

    # Categorize and format the products
    categorize_and_format_products(products, worksheet)

    # Save the file with unique naming
    possible_dirs = [
        'D:/COMMON DATA NEW/Handle ERP/Order PO',
        'D:/ORDERS',
        'Orders'
    ]

    orders_dir = None
    for directory in possible_dirs:
        if os.path.exists(directory) and os.path.isdir(directory):
            orders_dir = directory
            break

    if not orders_dir:
        orders_dir = 'Orders'
        os.makedirs(orders_dir, exist_ok=True)

    base_file_name = f'{client_id}_pending_{datetime.now().strftime("%d.%m.%y")}.xlsx'
    file_path = os.path.join(orders_dir, base_file_name)

    # Check if file exists and create a unique filename
    counter = 1
    while os.path.exists(file_path):
        file_name = f'{client_id}_pending_{datetime.now().strftime("%d.%m.%y")}({counter}).xlsx'
        file_path = os.path.join(orders_dir, file_name)
        counter += 1

    # Save the workbook
    workbook.save(file_path)
    logger.info(f"Pending order PO generated successfully: {file_path}")

    return {
        'message': 'Pending order PO generated successfully',
        'file_path': file_path
    }


@app.route('/update_order_details', methods=['POST'])
@app.route('/clear_order', methods=['POST'])
@cross_origin(origin='*', headers=['Content-Type', 'Authorization'])
def clear_order():
    """
    Deletes an order and its details.
    Expects JSON: {"orderId": <order_id_to_delete>}
    """
    logger.info("API request received: clear_order")
    data = request.get_json()

    if not data or 'orderId' not in data:
        logger.error("Missing orderId in clear_order request")
        return jsonify({"status": "error", "message": "orderId is required"}), 400

    order_id_to_delete = data['orderId']

    conn = None  # Initialize conn to None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if the order exists before attempting to delete
        cursor.execute("SELECT COUNT(*) FROM orders WHERE order_id = ?", (order_id_to_delete,))
        order_exists = cursor.fetchone()[0]

        if order_exists == 0:
            logger.warn(f"Attempted to delete non-existent orderId: {order_id_to_delete}")
            conn.close() # Close connection before returning
            return jsonify({"status": "error", "message": f"Order ID {order_id_to_delete} not found."}), 404

        # First, delete from order_details (due to potential foreign key constraints)
        cursor.execute("DELETE FROM order_details WHERE order_id = ?", (order_id_to_delete,))
        deleted_details_count = cursor.rowcount
        logger.info(f"Deleted {deleted_details_count} items from order_details for orderId: {order_id_to_delete}")

        # Then, delete from orders
        cursor.execute("DELETE FROM orders WHERE order_id = ?", (order_id_to_delete,))
        deleted_order_count = cursor.rowcount
        logger.info(f"Deleted {deleted_order_count} order from orders for orderId: {order_id_to_delete}")

        conn.commit()
        logger.info(f"Successfully deleted orderId: {order_id_to_delete} and its details.")
        return jsonify({
            "status": "success",
            "message": f"Order ID {order_id_to_delete} and its details have been successfully deleted."
        })

    except sqlite3.Error as db_error:
        if conn:
            conn.rollback()
        logger.error(f"Database error in clear_order for orderId {order_id_to_delete}: {str(db_error)}")
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(db_error)}"
        }), 500
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Unexpected error in clear_order for orderId {order_id_to_delete}: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Unexpected error: {str(e)}"
        }), 500
    finally:
        if conn:
            conn.close()
            logger.debug(f"Database connection closed for clear_order request (orderId: {order_id_to_delete}).")

#==============================================================================
#                         PACKING DEPARTMENT ROUTES
#==============================================================================
# These routes handle the packing process, carton management, and order completion

@app.route('/api/packing_state/<int:order_id>', methods=['GET'])
@app.route('/api/packing_state/<int:order_id>', methods=['POST'])
@app.route('/api/carton_details/<int:order_id>', methods=['GET'])
def get_carton_details(order_id):
    """Get detailed information about cartons for a specific order.

    This endpoint retrieves all carton details for a specific order ID,
    including which products are in each carton and their quantities.
    It supports both PCS and SETS quantities.

    Args:
        order_id (int): The ID of the order to retrieve carton details for

    Returns:
        JSON: Object with order information and carton details
    """
    logger.info(f"API request received: get_carton_details for order {order_id}")

    conn = get_db_connection()
    c = conn.cursor()

    try:
        # First verify the order exists and get basic order info
        c.execute("""
            SELECT o.client_id, o.order_date, o.status, o.scheduled_date
            FROM orders o
            WHERE o.order_id = ?
        """, (order_id,))
        order = c.fetchone()

        if not order:
            logger.warning(f"Order {order_id} not found")
            return jsonify({
                "status": "error",
                "message": f"Order {order_id} not found"
            }), 404

        # Get all cartons for this order
        c.execute("""
            SELECT DISTINCT carton_number
            FROM packed_products
            WHERE order_id = ?
            ORDER BY carton_number
        """, (order_id,))
        cartons_data = c.fetchall()

        # Prepare the response structure
        cartons = {}

        # For each carton, get the products it contains
        for carton in cartons_data:
            carton_number = carton[0]

            # Get the carton weight from the carton_weights table
            c.execute("""
                SELECT weight
                FROM carton_weights
                WHERE order_id = ? AND carton_number = ?
                LIMIT 1
            """, (order_id, carton_number))
            weight_result = c.fetchone()
            carton_weight = weight_result[0] if weight_result else None

            # Get products in this carton
            c.execute("""
                SELECT product_code, quantity_packed_pcs, quantity_packed_sets
                FROM packed_products
                WHERE order_id = ? AND carton_number = ?
            """, (order_id, carton_number))
            products = c.fetchall()

            # Format the products data
            formatted_products = []
            for product in products:
                formatted_products.append({
                    'productCode': product[0],
                    'quantityPackedPcs': product[1] or 0,
                    'quantityPackedSets': product[2] or 0
                })

            # Add this carton to the response
            cartons[str(carton_number)] = {
                'weight': carton_weight,
                'products': formatted_products
            }

        # Prepare the final response
        response = {
            'orderId': order_id,
            'clientId': order[0],
            'orderDate': order[1],
            'status': order[2],
            'scheduledDate': order[3],
            'cartons': cartons
        }

        logger.info(f"Returning details for {len(cartons)} cartons for order {order_id}")
        return jsonify(response)

    except sqlite3.Error as e:
        logger.error(f"Database error in get_carton_details: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(e)}"
        }), 500

    except Exception as e:
        logger.error(f"Unexpected error in get_carton_details: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"An unexpected error occurred: {str(e)}"
        }), 500

    finally:
        conn.close()


@app.route('/update_carton_weight', methods=['POST'])
def update_carton_weight():
    """Update the weight of a carton for a specific order.

    This endpoint allows updating or setting the weight of a carton.
    It adds or updates a record in the carton_weights table.

    Request body should contain:
    - order_id: The ID of the order
    - carton_number: The carton number
    - weight: The weight of the carton in kg

    Returns:
        JSON: Success or error message
    """
    logger.info("API request received: update_carton_weight")

    data = request.get_json()
    if not data or 'order_id' not in data or 'carton_number' not in data or 'weight' not in data:
        logger.error("Missing required parameters in update_carton_weight")
        return jsonify({
            'status': 'error',
            'message': 'order_id, carton_number, and weight are required'
        }), 400

    order_id = data['order_id']
    carton_number = data['carton_number']
    weight = data['weight']

    # Validate the weight
    try:
        weight = float(weight)
        if weight <= 0:
            raise ValueError("Weight must be positive")
    except ValueError as e:
        logger.error(f"Invalid weight value: {weight}")
        return jsonify({
            'status': 'error',
            'message': f'Invalid weight value: {str(e)}'
        }), 400

    conn = get_db_connection()
    c = conn.cursor()

    try:
        # First verify the order and carton exist
        c.execute("""
            SELECT 1 FROM packed_products
            WHERE order_id = ? AND carton_number = ?
            LIMIT 1
        """, (order_id, carton_number))

        if not c.fetchone():
            logger.warning(f"Carton {carton_number} not found for order {order_id}")
            conn.close()
            return jsonify({
                'status': 'error',
                'message': f'Carton {carton_number} not found for order {order_id}'
            }), 404

        # Get the product code for this carton (we'll use the first one if there are multiple)
        c.execute("""
            SELECT product_code FROM packed_products
            WHERE order_id = ? AND carton_number = ?
            LIMIT 1
        """, (order_id, carton_number))
        product_result = c.fetchone()
        product_code = product_result[0] if product_result else 'UNKNOWN'

        # Check if a weight record already exists for this carton
        c.execute("""
            SELECT id FROM carton_weights
            WHERE order_id = ? AND carton_number = ?
            LIMIT 1
        """, (order_id, carton_number))
        existing = c.fetchone()

        if existing:
            # Update existing record
            c.execute("""
                UPDATE carton_weights
                SET weight = ?, product_code = ?
                WHERE order_id = ? AND carton_number = ?
            """, (weight, product_code, order_id, carton_number))
            logger.info(f"Updated weight for carton {carton_number} of order {order_id} to {weight} kg")
        else:
            # Insert new record
            c.execute("""
                INSERT INTO carton_weights
                (order_id, product_code, carton_number, weight)
                VALUES (?, ?, ?, ?)
            """, (order_id, product_code, carton_number, weight))
            logger.info(f"Added weight for carton {carton_number} of order {order_id}: {weight} kg")

        conn.commit()
        conn.close()

        return jsonify({
            'status': 'success',
            'message': 'Carton weight updated successfully',
            'orderId': order_id,
            'cartonNumber': carton_number,
            'weight': weight
        })

    except sqlite3.Error as e:
        logger.error(f"Database error in update_carton_weight: {str(e)}")
        conn.close()
        return jsonify({
            'status': 'error',
            'message': f'Database error: {str(e)}'
        }), 500

    except Exception as e:
        logger.error(f"Unexpected error in update_carton_weight: {str(e)}")
        conn.close()
        return jsonify({
            'status': 'error',
            'message': f'An unexpected error occurred: {str(e)}'
        }), 500

@app.route('/pack_product', methods=['POST'])
@app.route('/order_details/<int:order_id>', methods=['GET'])
def get_order_details(order_id):
    """Get detailed information about a specific order.

    This endpoint retrieves all product details for a specific order ID.
    It returns information about each product in the order, including quantities
    for both PCS and SETS, and how many have been packed.

    Args:
        order_id (int): The ID of the order to retrieve details for

    Returns:
        JSON: Array of product details for the specified order
    """
    logger.info(f"API request received: get_order_details for order {order_id}")

    conn = get_db_connection()
    c = conn.cursor()

    try:
        # First verify the order exists
        c.execute("SELECT client_id FROM orders WHERE order_id = ?", (order_id,))
        order = c.fetchone()

        if not order:
            logger.warning(f"Order {order_id} not found")
            return jsonify({
                "status": "error",
                "message": f"Order {order_id} not found"
            }), 404

        # Get all product details for this order
        c.execute("""
            SELECT od.product_code, od.quantity_pcs, od.quantity_sets,
                   od.packed_pcs, od.packed_sets
            FROM order_details od
            WHERE od.order_id = ?
        """, (order_id,))
        
        details = [{
            "productCode": row[0],
            "quantity_pcs": row[1],  # Changed from quantityPcs to quantity_pcs
            "quantity_sets": row[2],  # Changed from quantitySets to quantity_sets
            "packed_pcs": row[3],     # Changed from packedPcs to packed_pcs
            "packed_sets": row[4],    # Changed from packedSets to packed_sets
            "clientId": order[0],
            # Calculate remaining quantities
            "remainingPcs": row[1] - row[3],
            "remainingSets": row[2] - row[4],
            # For backward compatibility
            "quantityToMake": row[1] + (row[2] * 2),  # 1 SET = 2 PCS
            "packed": row[3] + (row[4] * 2),          # 1 SET = 2 PCS
            "remainingQuantity": (row[1] - row[3]) + ((row[2] - row[4]) * 2)  # 1 SET = 2 PCS
        } for row in c.fetchall()]

        logger.info(f"Returning {len(details)} product details for order {order_id}")
        return jsonify(details)

    except sqlite3.Error as e:
        logger.error(f"Database error in get_order_details: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(e)}"
        }), 500

    finally:
        conn.close()


#==============================================================================
#                         DATABASE ROUTES
#==============================================================================
# These routes handle the database editor functionality for administrators


#==============================================================================
#                         HELPER FUNCTIONS
#==============================================================================
# These functions are used across multiple routes throughout the application
# Examples include generating PO Excel files, processing data, and utility operations

@app.route('/generate_po', methods=['POST'])
'''
This is the main backend server for the Trione Bath Corporation ERP system.
It handles all API requests, database operations, and serves the frontend files.

The server is built using Flask and provides a RESTful API for the frontend
to interact with the database. It also uses SocketIO for real-time updates.

Author: Harsh Nandha
Version: 1.4.6
Last Updated: May 2025
'''

#==============================================================================
#                             IMPORTS & SETUP
#==============================================================================
# Import everything we need from our centralized imports file
# This keeps our code clean and makes dependencies easy to manage

from imports import *
import time  # Added for timestamp-based fallback order IDs
from flask import abort, render_template, send_from_directory, g  # Added for manufacturing module and favicon
from werkzeug.utils import secure_filename  # Added for file upload handling

# Import manufacturing routes - needed for the manufacturing module
try:
    from manufacturing_routes import (
        serve_casting_order_page, serve_cnc_outward_page,
        serve_primary_store_receipt_page, serve_buffing_outward_page,
        serve_plating_outward_page, serve_assembly_receipt_page,
        register_manufacturing_blueprint 
    )
except ImportError:
    # Fallback if manufacturing_routes.py is not available
    print("Warning: manufacturing_routes.py not found - some manufacturing functions may not work")
    serve_casting_order_page = serve_cnc_outward_page = serve_primary_store_receipt_page = serve_buffing_outward_page = serve_plating_outward_page = serve_assembly_receipt_page = lambda: "Feature not available"
    register_manufacturing_blueprint = lambda app: print("Warning: Manufacturing blueprint not registered") # Add fallback

# Import purchase order routes - needed for the purchase order module
try:
    from purchase_order_routes import register_purchase_order_blueprint
except ImportError:
    # Fallback if purchase_order_routes.py is not available
    print("Warning: purchase_order_routes.py not found - purchase order functions may not work")
    register_purchase_order_blueprint = lambda app: print("Warning: Purchase Order blueprint not registered")

# Import report integration - needed for the reporting module
try:
    from report_integration import integrate_reports
except ImportError:
    # Fallback if report_integration.py is not available
    print("Warning: report_integration.py not found - reporting functions may not work")
    integrate_reports = lambda app: print("Warning: Reports integration not registered")

# Import WhatsApp service blueprint - needed for WhatsApp messaging
try:
    from whatsapp_service import register_whatsapp_service_blueprint
except ImportError:
    # Fallback if whatsapp_service.py is not available
    print("Warning: whatsapp_service.py not found - WhatsApp messaging functions may not work")
    register_whatsapp_service_blueprint = lambda app: print("Warning: WhatsApp service blueprint not registered")

# Set up logging so we can track what's happening in our application
# This is super helpful for debugging and monitoring the app's health
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.DEBUG  # Changed from logging.INFO to logging.DEBUG
)
logger = logging.getLogger(__name__)
logger.info("Initializing Trione Bath Corporation ERP Backend...")

#==============================================================================
#                         APPLICATION INITIALIZATION
#==============================================================================
# Create our Flask application - this is the heart of our backend
# The static_folder parameter tells Flask where to find static assets
# The template_folder parameter tells Flask where to find HTML templates
app = Flask(__name__, static_folder="static", template_folder=".")

# Configure template loader to look in multiple directories
from jinja2 import FileSystemLoader, ChoiceLoader
app.jinja_loader = ChoiceLoader([
    FileSystemLoader('.'),  # The default loader
    FileSystemLoader('FrontEnd'),  # Add FrontEnd directory for manufacturing templates
    FileSystemLoader('templates')  # Add templates directory for purchase order templates
])

# Enable Cross-Origin Resource Sharing (CORS)
# This allows our frontend to make requests to our backend even if they're on different domains
# It's essential for modern web applications with separate frontend and backend
CORS(app, resources={r"/*": {"origins": "*", "headers": ["Content-Type", "Authorization"]}})
logger.info("CORS configured for all origins")

# Set up SocketIO for real-time communication
# This lets us push updates to the frontend without the frontend having to poll for changes
# We're using eventlet as our async mode for better performance
socketio = SocketIO(app, async_mode='eventlet')
logger.info("SocketIO initialized with eventlet async mode")

# Add global keyboard shortcut script to all HTML responses
@app.after_request
def add_global_shortcuts(response):
    """Add global keyboard shortcuts script to all HTML responses."""
    excluded_paths = [
        '/packing',
        '/management',
        '/order_book',
        '/database_editor',
        '/main_dashboard',
        '/client_portal'
    ]
    if request.path in excluded_paths:
        return response

    if response.content_type == 'text/html; charset=utf-8' and not response.direct_passthrough:
        html = response.get_data(as_text=True)
        # Add the script before the closing body tag
        if '</body>' in html:
            shortcut_script = '<script src="/static/js/global-shortcuts.js"></script>'
            html = html.replace('</body>', f'{shortcut_script}\n</body>')
            response.set_data(html)
    return response

# Create a FileHandler for request logging
import os
from datetime import datetime
import json

# Request logging middleware
@app.before_request
def log_request():
    """Log details about incoming requests."""
    # Skip logging for static resources
    if request.path.startswith('/static/') or request.path.startswith('/frontend/'):
        return
    
    # Store request start time for duration calculation
    g.request_start_time = datetime.now()

@app.after_request
def log_response(response):
    """Process the response."""
    # Skip for static resources
    if request.path.startswith('/static/') or request.path.startswith('/frontend/'):
        return response
    
    return response

#==============================================================================
#                         DATABASE CONFIGURATION
#==============================================================================
# We're using SQLite for our database - it's simple, reliable, and   doesn't require
# a separate server. Perfect for this application!
DATABASE_PATH = 'Trione_ERP_Database.db'
logger.info(f"Database path set to: {DATABASE_PATH}")

def get_db_connection():
    """Hearken, ye seekers of digital knowledge! Herein lies the mystical incantation
    that doth forge a sacred connection to ye olde SQLite database of wonders.

    Like a wise alchemist mixing potions of great power, this function doth create
    a fresh connection to our repository of knowledge each time it is summoned.
    We doth set the row_factory to sqlite3.Row, a most cunning enchantment that
    alloweth us to access columns by their given names, rather than by mere numerical
    indices, as would befit only the most primitive of database practitioners.

    Verily, this function is the very keystone upon which our entire kingdom of data
    doth rest! Without it, we would be but wanderers in a digital wasteland, unable
    to retrieve or store the precious information that is the lifeblood of our realm.

    Returns:
        sqlite3.Connection: A most powerful and active connection to ye database,
        ready to do thy bidding and execute thy SQL commands with great haste.
    """
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row  # This lets us access columns by name cause that is what we want
    return conn

def init_db():
    """Hark! Attend all ye who seek to understand the ancient art of database creation!

    Herein lies the sacred ritual of initialization, whereby the grand tables of our
    ERP system are conjured into existence, if they have not already been manifested
    in the digital realm. Fear not, brave administrator, for this mystical ceremony
    may be performed multiple times without risk - it shall not vanquish thy existing
    data into the void of digital oblivion.

    Behold the mighty tables that shall be forged by this enchantment:

    - client_logos: A repository most noble for the heraldic emblems of our clients,
      that their banners may fly proudly throughout our application's domain

    - orders: The grand ledger wherein all customer commands are inscribed,
      a record of commerce that shall endure through the ages

    - order_details: A detailed chronicle of each order's contents, listing
      every product and quantity with the precision of a master scribe

    - order_counter: A humble yet essential guardian that keepeth track of the
      last order ID, ensuring that each new decree receives its proper designation

    - packed_products: A vigilant observer of the packing process, recording
      which treasures have been secured for transport to distant lands

    - carton_weights: A meticulous record-keeper who noteth the heft of each
      carton, information most crucial for the calculation of shipping costs

    - notes: A collection of scrolls containing system-wide proclamations and
      reminders, that no important task shall be forgotten

    - users: The registry of all who may access our digital kingdom, along with
      their granted permissions and secret passwords of entry

    Verily, without these tables, our application would be as a castle built upon sand,
    unable to retain information or serve its noble purpose! Let the initialization
    commence!
    """
    logger.info("Initializing database tables...")
    conn = get_db_connection()
    c = conn.cursor()

    # Create client_logos table - stores paths to client logo images
    # This helps us display client logos throughout the application
    logger.info("Setting up client_logos table...")
    c.execute('''
        CREATE TABLE IF NOT EXISTS client_logos
            (client_id TEXT PRIMARY KEY, logo_path TEXT)
    ''')

    # Create orders table - this is where we track all customer orders
    # Each order has a unique ID, client info, status, and various dates
    logger.info("Setting up orders table...")
    c.execute('''
        CREATE TABLE IF NOT EXISTS orders
            (id INTEGER PRIMARY KEY, order_id INTEGER, client_id TEXT,
            order_date TEXT, status TEXT, is_today INTEGER DEFAULT 0,
            line TEXT, packed_date TEXT, scheduled_date TEXT,
            priority INTEGER DEFAULT 0)
    ''')

    # Create order_details table - stores the individual line items for each order
    # This tracks what products are in each order and their quantities (both PCS and SETS)
    logger.info("Setting up order_details table...")
    c.execute('''
        CREATE TABLE IF NOT EXISTS order_details
            (id INTEGER PRIMARY KEY, order_id INTEGER, product_code TEXT,
            quantity_pcs INTEGER DEFAULT 0, quantity_sets INTEGER DEFAULT 0,
            packed_pcs INTEGER DEFAULT 0, packed_sets INTEGER DEFAULT 0)
    ''')

    # Create order_counter table - keeps track of the last order ID used
    # This ensures we always have unique, sequential order IDs
    logger.info("Setting up order_counter table...")
    c.execute('''
        CREATE TABLE IF NOT EXISTS order_counter
            (id INTEGER PRIMARY KEY, last_order_id INTEGER)
    ''')

    # Insert initial record in order_counter if it doesn't exist
    c.execute("SELECT COUNT(*) FROM order_counter WHERE id = 1")
    if c.fetchone()[0] == 0:
        logger.info("Inserting initial record in order_counter table")
        c.execute("INSERT INTO order_counter (id, last_order_id) VALUES (1, 1000)")

    # Create packed_products table - tracks which products have been packed
    # This helps us manage the packing process and track cartons
    # We track both PCS and SETS quantities for packed products
    logger.info("Setting up packed_products table...")
    c.execute('''
        CREATE TABLE IF NOT EXISTS packed_products
            (id INTEGER PRIMARY KEY, order_id INTEGER, product_code TEXT,
            carton_number INTEGER, quantity_packed_pcs INTEGER DEFAULT 0,
            quantity_packed_sets INTEGER DEFAULT 0)
    ''')

    # Create order_packing_state table - tracks the current carton number for each order
    # This helps us manage the packing process and keep track of which carton we're on
    logger.info("Setting up order_packing_state table...")
    c.execute('''
        CREATE TABLE IF NOT EXISTS order_packing_state
            (id INTEGER PRIMARY KEY, order_id INTEGER UNIQUE,
            current_carton_number INTEGER DEFAULT 1)
    ''')

    # Create carton_weights table - tracks weights of packed cartons
    # This is important for shipping and quality control
    logger.info("Setting up carton_weights table...")
    c.execute('''
        CREATE TABLE IF NOT EXISTS carton_weights
            (id INTEGER PRIMARY KEY, order_id INTEGER NOT NULL,
            product_code TEXT NOT NULL, carton_number INTEGER NOT NULL,
            weight REAL NOT NULL,
            FOREIGN KEY (order_id) REFERENCES orders(id),
            FOREIGN KEY (product_code) REFERENCES order_details(product_code),
            FOREIGN KEY (carton_number) REFERENCES packed_products(carton_number))
    ''')

    # Create notes table - for system-wide notes and reminders
    # These can be assigned to departments and marked as completed
    logger.info("Setting up notes table...")
    c.execute('''
        CREATE TABLE IF NOT EXISTS notes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            content TEXT NOT NULL,
            author TEXT NOT NULL,
            department TEXT,
            is_completed INTEGER DEFAULT 0,
            completed_by TEXT,
            completed_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create users table - for authentication and access control
    # This manages who can access what in the system
    logger.info("Setting up users table...")
    c.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT NOT NULL,
            access_permissions TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
    ''')

    # Create product_names table - stores product codes and their corresponding names
    # This helps with product identification and display throughout the application
    logger.info("Setting up product_names table...")
    c.execute('''
        CREATE TABLE IF NOT EXISTS product_names (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_code TEXT UNIQUE NOT NULL,
            product_name TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Insert default admin user if not exists
    # This ensures there's always at least one admin user to access the system
    logger.info("Setting up default admin user...")
    c.execute('''INSERT OR IGNORE INTO users (username, password, role, access_permissions)
               VALUES (?, ?, ?, ?)''',
              ('ADMIN-TRIONE', 'TRIONE@123', 'super_admin', json.dumps({
                  "publicPages": ["/order_book", "/packing", "/store"],
                  "adminPages": ["/management", "/accounts", "/production", "/tablet",
                           "/database_editor", "/master", "/clientnoteadd", "/client_report",
                           "/client_wise_order", "/notes", "/production_manager"],
                  "superAdminPages": ["/admin"]
              })))

    # Save all our changes and close the connection
    conn.commit()
    conn.close()

    # Initialize product names
    init_product_names()

    # Initialize manufacturing database tables
    init_manufacturing_db()

    # Update manufacturing database schemas
    try:
        import db_schema
        # Allow update_final_store_transaction_schema to manage its own connection
        # by not passing the (now closed) conn and c from Trione_Backend.py's init_db.
        # This function defaults to using 'manufacturing.db'.
        db_schema.update_final_store_transaction_schema() 
        logger.info("Manufacturing database schemas updated successfully")
    except Exception as e:
        logger.error(f"Error updating manufacturing database schemas: {e}")

    # Initialize purchase order database tables
    try:
        from purchase_order_db_setup import init_purchase_order_db
        init_purchase_order_db()
    except ImportError:
        logger.warning("purchase_order_db_setup.py not found - purchase order tables may not be initialized")

    logger.info("✓ Database initialized successfully!")

#==============================================================================
#                         DATA ACCESS FUNCTIONS
#==============================================================================
# These functions handle database operations and data retrieval
# They provide a clean interface for accessing and manipulating data

def init_manufacturing_db():
    """Oyez! Oyez! Gather round, ye masters of industry and crafters of fine bath wares!

    This most potent incantation doth summon forth the tables of manufacturing into
    our grand database, should they not already exist in the digital firmament.
    Like a master craftsman preparing his workshop before the day's labor begins,
    this function doth ensure that all the necessary structures are in place for
    the recording of our manufacturing processes.

    Behold as we attempt to add the mystical 'material_type' column to the sacred
    product_master table, allowing us to distinguish between the noble metals of
    Brass and Zinc! Should this column already exist, fear not, for our spell shall
    continue undeterred, acknowledging that this part of the workshop is already
    properly furnished.

    Then, with the wisdom of the ancients, we shall consult the sacred scroll of
    'manufacturing_db_setup.sql', wherein lies the complete blueprint for our
    manufacturing tables. This venerable document contains the accumulated knowledge
    of generations of database architects, ensuring that our manufacturing data
    shall be stored with perfect structure and harmony.

    Verily, without these tables, our manufacturing processes would be as a ship
    without a rudder, unable to track the flow of materials through our noble
    production lines! Let the initialization of manufacturing commence!
    """
    logger.info("Initializing manufacturing database tables...")
    conn = get_db_connection()
    try:
        # First try to add material_type column if it doesn't exist
        try:
            conn.execute("ALTER TABLE product_master ADD COLUMN material_type TEXT DEFAULT 'Brass' NOT NULL CHECK (material_type IN ('Brass', 'Zinc'))")
            logger.info("Added material_type column to product_master table")
        except Exception as e:
            # Column might already exist, which is fine
            logger.debug(f"Note: {e}")

        # Run the full schema to ensure all tables have the correct structure
        with open('manufacturing_db_setup.sql', 'r') as sql_file:
            sql_script = sql_file.read()
        conn.executescript(sql_script)
        conn.commit()
        logger.info("Manufacturing database tables initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing manufacturing database: {e}")
    finally:
        conn.close()

# Helper function to parse date strings
def parse_date(date_str):
    """Hearken, ye who seek to understand the mysteries of time's passage!

    This enchanted function doth take a humble string of characters and transmuteth
    it into a proper date object, provided it followeth the sacred format of
    YYYY-MM-DD, as decreed by the ISO standards council of digital timekeeping.

    Like a wise astronomer interpreting the positions of celestial bodies, this
    function doth read the runes of time and convert them into a form that our
    application can comprehend and utilize. Should the string be empty or None,
    the function shall return None, signifying the absence of temporal information.

    But beware! Should ye provide a date string that violateth the sacred format,
    this function shall raise a mighty 400 error, casting the offender out of our
    digital realm with a stern message about proper date formatting! For in the
    kingdom of data, precision is not merely desired - it is required!

    Args:
        date_str (str): The mystical sequence of characters representing a date

    Returns:
        date: A properly formed date object, ready for use in our application,
              or None if the input is None, signifying the absence of a date

    Raises:
        400 error: If the date format doth not conform to the sacred YYYY-MM-DD pattern,
                   for such transgressions cannot be tolerated in our ordered realm
    """
    if not date_str:
        return None
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        abort(400, description="Invalid date format. Use YYYY-MM-DD")

def init_product_names():
    """Prithee, attend! For here beginneth the grand christening of our products!

    This most noble function doth populate the hallowed product_names table with
    the sacred mappings betwixt product codes and their proper appellations. Like
    a royal herald announcing the titles and lineages of noble guests at court,
    this function proclaims the true identities of our bath fixtures, that they
    may be known throughout the realm by their proper names, not merely by their
    cryptic numerical designations.

    With the wisdom of Solomon, this function doth check whether each product
    hath already been recorded in our grand registry. If a product's name hath
    already been inscribed in the digital scrolls, it shall not be duplicated,
    for redundancy is the enemy of efficient data management, as the ancient
    database sages have taught us.

    Behold the grand ceremony wherein BARCELONA, HAMBURG, SARA, and many other
    distinguished members of our product lineage shall receive their formal
    recognition! Without this ritual, our products would wander nameless through
    the digital realm, known only by cold numbers rather than their majestic titles.

    Let the naming commence!
    """
    logger.info("Initializing product names...")

    # Product code to name mapping
    product_mappings = [
        ("02", "BARCELONA"),
        ("06", "HAMBURG"),
        ("155", "SARA"),
        ("149", "GIA"),
        ("148", "MATTEO"),
        ("152", "SIA"),
        ("104", "RIGA"),
        ("110 (B)", "VIENNA"),
        ("146", "ROZA"),
        ("144", "GALA"),
        ("110 (C)", "SOFIA"),
        ("112", "FLORANCE"),
        ("115", "VALENCIA"),
        ("120", "MUNICH"),
        ("118", "VENICE"),
        ("127", "ELVA"),
        ("150", "MADRID"),
        ("151", "ASTRA"),
        ("135", "ROMI"),
        ("114 (B)", "BETA"),
        ("136", "ALFA"),
        ("137", "EVRA"),
        ("138", "VITA"),
        ("114", "MILAN"),
        ("134", "SAGA"),
        ("133", "NOVI"),
        ("129", "PANNA"),
        ("126", "ZITA"),
        ("124", "SOREN"),
        ("116", "ATHENS"),
        ("111", "PORTO"),
        ("07", "OSLO"),
        ("101", "MOSCO"),
        ("130", "TAS"),
        ("132", "CUBE"),
        ("131", "SKY"),
        ("153", "LUCAS"),
        ("154", "DORA"),
        ("141", "ORRIN"),
        ("142", "ZENN"),
        ("143", "NIVA"),
        ("139", "VEGA"),
        ("140", "LUNAR"),
        ("147", "DORIAN")
    ]

    conn = get_db_connection()
    c = conn.cursor()

    # Insert each product mapping if it doesn't already exist
    for code, name in product_mappings:
        c.execute("SELECT COUNT(*) FROM product_names WHERE product_code = ?", (code,))
        if c.fetchone()[0] == 0:
            c.execute("INSERT INTO product_names (product_code, product_name) VALUES (?, ?)",
                     (code, name))
            logger.info(f"Added product mapping: {code} -> {name}")

    # Commit changes and close connection
    conn.commit()
    conn.close()
    logger.info("Product names initialization complete")

def get_product_name(product_code):
    """Hark! This mystical function doth reveal the true name behind the cryptic code!

    Like a wise sage deciphering ancient runes, this function taketh a product code
    and seeketh its corresponding name in the sacred product_names table. It doth
    handle the complexities of our product coding system with great cunning, extracting
    the base code from combinations that may include underscores or parentheses.

    Verily, this function showeth special wisdom when confronted with the enigmatic
    codes of "110 (B)", "110(C)", or "114 (B)", recognizing their special nature
    and treating them with the respect due to their unique identities. Such codes
    are not merely variants, but distinct products in their own right!

    Should the function find a matching name in our grand registry, it shall return
    this true name with great triumph. But should no name be found - perchance because
    the product is new to our realm or hath been entered with an unknown code - the
    function shall graciously return the original code, ensuring that our application
    continueth to function even when faced with mysterious product designations.

    Args:
        product_code (str): The enigmatic code whose true name we seek to unveil

    Returns:
        str: The product's true name if it be found in our records, or the original
             code if the product remaineth unidentified in our system
    """
    # Extract base code (before underscore or parentheses)
    base_code = product_code.split('_')[0] if '_' in product_code else product_code
    base_code = base_code.split(' ')[0] if ' ' in base_code else base_code

    # Handle special cases like "110 (B)" or "110(C)"
    if base_code == "110" and "(" in product_code:
        if "(B)" in product_code:
            base_code = "110 (B)"
        elif "(C)" in product_code:
            base_code = "110(C)"
    elif base_code == "114" and "(B)" in product_code:
        base_code = "114 (B)"

    conn = get_db_connection()
    c = conn.cursor()
    c.execute("SELECT product_name FROM product_names WHERE product_code = ?", (base_code,))
    result = c.fetchone()
    conn.close()

    if result:
        return result[0]
    else:
        return product_code  # Return the code itself if no name is found

def fetch_data_from_sqlite(db_name="client_db.db", table_name="clients"):
    """Hearken! For here lies the mystical art of data extraction from the SQLite realm!

    This enchanted function doth journey into the depths of a SQLite database,
    like a brave knight entering a dragon's lair, to retrieve the precious treasures
    of data that lie within. It seeketh out a specific table within the database,
    and bringeth forth all rows contained therein, transforming them from their
    raw form into a collection of dictionaries that our application can easily comprehend.

    With the skill of a master alchemist, this function transmuteth the raw data
    into a form where each row becometh a dictionary, with column names serving as
    keys to access the values within. This transformation maketh the data ready for
    JSON serialization, allowing it to be sent forth across the digital realm to
    the frontend, where it may be displayed to the user in all its glory.

    Should the database prove uncooperative, or should the table be missing or
    corrupted, this function shall not despair! Instead, it shall return an empty
    list, signifying that no data could be retrieved, and log the nature of the
    misfortune for future investigation by the database administrators.

    By default, this function seeketh the 'clients' table within the 'client_db.db'
    database, but it can be directed to seek other treasures in other locations
    if so commanded.

    Args:
        db_name (str): The name of the mystical SQLite database file wherein
                       the desired data doth reside
        table_name (str): The name of the specific table whose contents we seek

    Returns:
        list: A grand collection of dictionaries, each representing a row from
              the table, with column names as keys to access the values within
    """
    logger.info(f"Fetching all data from {table_name} in {db_name}")

    # Create a connection to the specified database
    conn = sqlite3.connect(db_name)
    cursor = conn.cursor()

    try:
        # Fetch all data from the table
        cursor.execute(f"SELECT * FROM {table_name}")
        rows = cursor.fetchall()

        # Get column names from the cursor description
        columns = [description[0] for description in cursor.description]

        # Convert rows to list of dictionaries for easier JSON serialization
        # This maps column names to their values for each row
        data = [dict(zip(columns, row)) for row in rows]

        logger.info(f"Successfully retrieved {len(data)} records from {table_name}")
        return data

    except sqlite3.Error as e:
        # Log any database errors that occur
        logger.error(f"Database error while fetching from {table_name}: {str(e)}")
        return []
    finally:
        # Always close the connection to avoid resource leaks
        conn.close()

#==============================================================================
#                         FRONTEND ROUTES
#==============================================================================
# These routes serve our HTML pages to the user's browser
# Each route maps a URL path to a specific HTML file in our frontend folder

#-------------------------------------------------------
# Favicon Route
#-------------------------------------------------------

@app.route('/favicon.ico')
def favicon():
    """Hærken ye, O noble travellers of ye digital realm! Herein lies ye magickal route
    for ye moste importante icon of favicon, whych doth adorn ye browser tabs wyth
    splendour and majestie. In days of yore, when knights and squires did browse ye
    internette wyth Internet Explorer 6, such icons were but a dreame. But lo! We have
    progressed into an age of wondrous technology, where even ye smallest pixel doth
    carry meaning and purpose. This sacred route shall deliver unto thine browser ye
    holy symbol of Trione Bath Corporation, crafted wyth care by ye finest digital
    artisans in all ye land. May it forever grace thine browser tab wyth its divine
    presence, and may all who gaze upon it know ye have arrived at ye official realm
    of Trione. Forsooth, it is but a humble icon, yet it doth represent ye entire
    kingdom of our application. Guard it well, for without it, users might mistake
    our noble application for some common website of ill repute! Ye shall find this
    treasure in ye root folder, where it hath been placed by ye wise developers of old.
    """
    return send_from_directory('.', 'favicon.ico')

#-------------------------------------------------------
# Manufacturing Routes
#-------------------------------------------------------

@app.route('/manufacturing')
def manufacturing_home():
    """Oyez! Oyez! Welcome to ye olde Manufacturing Realm of Trione Bath Corporation!

    This most noble route doth serve as the grand entrance to our manufacturing domain,
    where the mystical arts of bath fixture creation are practiced with great skill
    and precision. Like a royal herald announcing the arrival of distinguished guests
    at court, this route doth welcome visitors to the manufacturing section of our
    application, directing them to the manufacturing dashboard where they may observe
    and manage the creation of our fine products.

    When a weary traveler of the digital realm doth visit this URL, they shall be
    transported forthwith to the manufacturing dashboard, where they may gaze upon
    the status of our production lines, monitor the flow of materials, and issue
    commands to our skilled craftsmen. This route is but a gateway, redirecting
    visitors to the true heart of our manufacturing operations.

    Verily, without this route, visitors would wander lost in the digital wilderness,
    unable to find their way to our manufacturing operations! Let them enter and
    behold the wonders of bath fixture production!
    """
    return serve_manufacturing_dashboard()

@app.route('/manufacturing/dashboard')
def manufacturing_dashboard():
    """Hark! Behold the grand observatory of our manufacturing operations!

    This most illustrious route doth present the manufacturing dashboard, a magical
    window into the very heart of our production processes. Like the command center
    of a mighty castle, this dashboard provideth a comprehensive view of all manufacturing
    activities, allowing the wise overseer to monitor and direct the creation of our
    fine bath fixtures with great efficiency and precision.

    Upon this dashboard, one may observe the status of various production stages,
    from the initial casting of raw materials to the final assembly and packaging
    of completed products. Charts and graphs doth display production metrics with
    the clarity of a mountain stream, while tables of data reveal the detailed
    status of each work order currently progressing through our workshops.

    The manufacturing dashboard is truly the beating heart of our production realm,
    the central nexus from which all manufacturing activities are observed and
    directed. Without it, our production managers would be as blind men attempting
    to navigate a labyrinth, unable to see the status of their domain or make
    informed decisions about resource allocation and scheduling.

    Let all who seek to understand the state of our manufacturing operations
    come hither and gaze upon this dashboard of wonders!
    """
    return serve_manufacturing_dashboard()

@app.route('/manufacturing/scrap_entry')
def manufacturing_scrap_entry():
    """Harken, ye who must record the fallen soldiers of our production battlefield!

    This solemn route doth serve the scrap entry page, where the unfortunate casualties
    of our manufacturing process are dutifully recorded and accounted for. Like a
    medieval scribe documenting the losses after a great battle, this page alloweth
    our production managers to record materials and components that have been damaged,
    malformed, or otherwise rendered unsuitable for inclusion in our fine products.

    With great care and attention to detail, users of this page may enter information
    about scrapped items, including the type of material, the quantity lost, the
    stage of production at which the loss occurred, and the reason for the scrapping.
    This information is most precious for our quality control efforts and for the
    accurate calculation of material costs and production efficiency.

    Verily, though it pains us to acknowledge the existence of scrap in our
    manufacturing process, we must face this reality with courage and diligence.
    By accurately tracking our losses, we gain the wisdom to improve our processes
    and reduce waste in the future, thus honoring the sacrifice of these materials
    by ensuring that fewer of their brethren shall meet a similar fate.

    Let all who bear witness to manufacturing imperfections come forth and record
    them here, that our production might grow ever more efficient and our quality
    ever more exalted!
    """
    return serve_scrap_entry_page()

@app.route('/manufacturing/api/scrap_entries', methods=['GET'])
def manufacturing_get_scrap_entries():
    """Hearken, ye seekers of knowledge about our manufacturing losses!

    This mystical API endpoint doth serve as a gateway to the ancient records of
    scrap entries in our manufacturing process. Like a royal librarian retrieving
    scrolls from a vast archive, this function fetcheth the recent history of
    materials and components that have been deemed unfit for inclusion in our
    fine products, and presenteth this information in the sacred format of JSON.

    When a digital emissary doth approach this endpoint with a GET request, the
    function shall query our database for the most recent scrap entries, gathering
    details about the type of material lost, the quantity, the stage of production
    at which the loss occurred, and the reason for the scrapping. This information
    is then transformed into a format suitable for transmission across the digital
    realm, that it may be displayed to users on the scrap entry page.

    This endpoint serveth a most crucial role in our quality control efforts,
    providing the data necessary for analysis of production inefficiencies and
    identification of recurring issues. By studying the patterns revealed in these
    records, our production managers may gain the wisdom to implement improvements
    that reduce waste and enhance the quality of our manufacturing processes.

    Let all who seek to understand our manufacturing losses send forth their GET
    requests to this endpoint, that they may receive the knowledge they seek!
    """
    return get_scrap_entries_api()

@app.route('/manufacturing/api/scrap_entries', methods=['POST'])
def manufacturing_add_scrap_entry():
    """Hark! This sacred portal doth accept the solemn records of manufacturing sacrifice!

    This most important API endpoint doth receive the detailed accounts of materials
    and components that have been lost during our manufacturing process. Like a
    medieval priest recording confessions in a great ledger, this function accepteth
    the particulars of each scrapped item and enshrines them in our database for
    posterity and analysis.

    When a digital messenger approacheth this endpoint bearing a POST request laden
    with information about a scrapped item, the function shall carefully extract
    this information - the material type, quantity, production stage, and reason for
    scrapping - and commit it to our database with all due ceremony. Should the
    information be complete and properly formatted, the function shall respond with
    a message of success; but should there be errors or omissions, it shall return
    an appropriate error message, that the sender might correct their submission.

    This endpoint serveth as the primary means by which our scrap tracking system
    is populated with data. Without it, our records of manufacturing losses would
    remain empty, and we would be blind to the patterns of waste that might be
    addressed to improve our production efficiency. It is through the diligent use
    of this endpoint that we gain the knowledge necessary to refine our processes
    and reduce the quantity of materials that must be sacrificed to the gods of
    manufacturing imperfection.

    Let all who witness the unfortunate scrapping of materials send forth their
    records to this endpoint, that our database might be complete and our analysis
    accurate!
    """
    return add_scrap_entry_api()

@app.route('/manufacturing/casting')
def manufacturing_casting():
    """Oyez! Behold the sacred chamber where molten metal taketh form!

    This most wondrous route doth serve the casting management page, where the
    first and most fundamental stage of our manufacturing process is overseen.
    Like the fiery workshop of Vulcan himself, this is where raw materials are
    transformed through the ancient art of casting into the basic forms that shall
    eventually become our fine bath fixtures.

    Upon this page, production managers may observe the status of casting operations,
    track the flow of raw materials into the casting process, monitor the output of
    cast components, and manage the work orders currently in the casting stage.
    They may also record the unfortunate instances where castings must be scrapped
    due to defects or other issues, ensuring that our records remain accurate and
    our material usage properly accounted for.

    The casting stage is truly the foundation upon which all subsequent manufacturing
    processes are built. Without properly cast components, the later stages of
    machining, finishing, and assembly cannot proceed. Thus, this page serveth a
    most crucial role in our production management system, allowing for the careful
    oversight of this fundamental process.

    Let all who would understand the mysteries of metal casting come forth and
    gaze upon this page, that they might appreciate the skill and precision required
    to transform raw materials into the beginnings of our fine products!
    """
    return serve_casting_page()

@app.route('/manufacturing/casting_order')
def manufacturing_casting_order():
    """Hearken! This portal leadeth to the sacred scroll of casting commands!

    This most important route doth serve the casting order page, where the wise
    production managers may issue formal decrees for the creation of cast components.
    Like a royal chancellor drafting proclamations for the kingdom, this page alloweth
    for the creation of official orders that direct our casting operations to produce
    specific quantities of particular components.

    Upon this page, one may specify the product code to be cast, the quantity required
    (in both pieces and sets), the destination to which the castings shall be sent
    upon completion, and other vital details necessary for the proper execution of
    the casting process. These orders form the basis of our production planning,
    ensuring that our casting operations are aligned with the needs of subsequent
    manufacturing stages and, ultimately, with customer demand.

    The casting order system is a most crucial link in the chain of command that
    floweth from customer orders to finished products. Without clear and precise
    casting orders, our production would be as a ship without a rudder, unable to
    navigate the complex waters of manufacturing with purpose and direction.

    Let all who would direct the creation of cast components come forth and issue
    their commands through this most noble interface, that our production might
    proceed with order and efficiency!
    """
    return serve_casting_order_page()

@app.route('/manufacturing/api/casting', methods=['GET'])
def manufacturing_get_casting_entries():
    """Hark! This mystical gateway revealeth the ancient records of our casting endeavors!

    This most valuable API endpoint doth serve as a portal to the historical archives
    of our casting operations. Like a royal librarian retrieving ancient scrolls from
    the deepest vaults of the kingdom's repository, this function fetcheth the records
    of past casting activities and presenteth them in the sacred format of JSON.

    When a digital emissary approacheth this endpoint with a GET request, the function
    shall query our database for records of casting operations, gathering details about
    the products cast, the quantities produced, the dates of casting, and other vital
    information. This data is then transformed into a format suitable for transmission
    across the digital realm, that it may be displayed to users on the casting management
    page or utilized by other components of our application.

    This endpoint serveth a most crucial role in our production tracking system,
    providing the data necessary for analysis of casting efficiency, material usage,
    and production trends. By studying the patterns revealed in these records, our
    production managers may gain the wisdom to optimize our casting operations,
    allocate resources more effectively, and plan future production with greater accuracy.

    Let all who seek to understand the history of our casting operations send forth
    their GET requests to this endpoint, that they may receive the knowledge they seek!
    """
    return get_casting_entries_api()

@app.route('/manufacturing/api/casting', methods=['POST'])
def manufacturing_add_casting_entry():
    """Oyez! This sacred portal accepteth the records of newly forged metal components!

    This most important API endpoint doth receive the detailed accounts of casting
    operations that have been completed in our workshops. Like a royal scribe recording
    the achievements of the kingdom's craftsmen, this function accepteth the particulars
    of each casting batch and enshrines them in our database for posterity and analysis.

    When a digital messenger approacheth this endpoint bearing a POST request laden
    with information about a completed casting operation, the function shall carefully
    extract this information - the casting date, product code, quantity produced (in
    both pieces and weight), and the per-piece weight - and commit it to our database
    with all due ceremony. Should the information be complete and properly formatted,
    the function shall respond with a message of success; but should there be errors
    or omissions, it shall return an appropriate error message, that the sender might
    correct their submission.

    This endpoint serveth as the primary means by which our casting production records
    are populated with data. Without it, our records of casting operations would remain
    empty, and we would be blind to the output of this most fundamental manufacturing
    process. It is through the diligent use of this endpoint that we gain the knowledge
    necessary to track our production, manage our inventory, and plan our operations
    with wisdom and foresight.

    Furthermore, this noble function doth perform the additional service of tracking
    material consumption, automatically creating the appropriate entries in our scrap
    inventory to reflect the metal that hath been transformed from raw material into
    cast components. This ensures that our material tracking remaineth accurate and
    our inventory counts reliable.

    Let all who oversee the completion of casting operations send forth their records
    to this endpoint, that our database might be complete and our analysis accurate!
    """
    data = request.json
    if not all(k in data for k in ['casting_date', 'product_code', 'quantity_pcs', 'quantity_kg']):
        return jsonify({'status': 'error', 'message': 'Missing required fields'}), 400

    casting_date = parse_date(data['casting_date'])
    product_code = data['product_code']
    quantity_pcs = int(data['quantity_pcs'])
    quantity_kg = float(data['quantity_kg'])

    # Calculate per piece weight if not provided (in grams)
    per_piece_weight = data.get('per_piece_weight')
    if not per_piece_weight:
        per_piece_weight = (quantity_kg * 1000) / quantity_pcs if quantity_pcs > 0 else 0
    else:
        per_piece_weight = float(per_piece_weight)

    entry_id = add_casting_entry(casting_date, product_code, quantity_pcs, quantity_kg, per_piece_weight)

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'Casting entry added successfully'
    }), 201

@app.route('/manufacturing/cnc')
def manufacturing_cnc():
    """Serve the CNC page."""
    return serve_cnc_page()

@app.route('/manufacturing/api/cnc', methods=['GET'])
def manufacturing_get_cnc_transactions():
    """API endpoint to retrieve CNC transactions."""
    return get_cnc_transactions_api()

@app.route('/manufacturing/api/cnc', methods=['POST'])
def manufacturing_add_cnc_transaction():
    """API endpoint to add a new CNC transaction."""
    return add_cnc_transaction_api()

@app.route('/manufacturing/cnc_outward')
def manufacturing_cnc_outward():
    """Serve the CNC outward page."""
    return serve_cnc_outward_page()

@app.route('/manufacturing/primary_store')
def manufacturing_primary_store():
    """Serve the primary store page."""
    return serve_primary_store_page()

@app.route('/manufacturing/api/primary_store', methods=['GET'])
def manufacturing_get_primary_store_inventory():
    """API endpoint to retrieve primary store inventory."""
    return get_primary_store_inventory_api()

@app.route('/manufacturing/api/primary_store', methods=['POST'])
def manufacturing_add_primary_store_transaction():
    """API endpoint to add a new primary store transaction."""
    return add_primary_store_transaction_api()

@app.route('/manufacturing/primary_store_receipt')
def manufacturing_primary_store_receipt():
    """Serve the primary store receipt page."""
    return serve_primary_store_receipt_page()

@app.route('/manufacturing/buffing')
def manufacturing_buffing():
    """Serve the buffing page."""
    return serve_buffing_page()

@app.route('/manufacturing/api/buffing', methods=['GET'])
def manufacturing_get_buffing_transactions():
    """API endpoint to retrieve buffing transactions."""
    return get_buffing_transactions_api()

@app.route('/manufacturing/api/buffing', methods=['POST'])
def manufacturing_add_buffing_transaction_route():
    """API endpoint to add a new buffing transaction."""
    return add_buffing_transaction_api()

@app.route('/manufacturing/buffing_outward')
def manufacturing_buffing_outward():
    """Serve the buffing outward page."""
    return serve_buffing_outward_page()

@app.route('/manufacturing/final_store')
def manufacturing_final_store():
    """Serve the final store page."""
    return serve_final_store_page()

@app.route('/manufacturing/api/final_store', methods=['GET'])
def manufacturing_get_final_store_inventory():
    """API endpoint to retrieve final store inventory."""
    return get_final_store_inventory_api()

@app.route('/manufacturing/api/final_store', methods=['POST'])
def manufacturing_add_final_store_transaction():
    """API endpoint to add a new final store transaction."""
    return add_final_store_transaction_api()

@app.route('/manufacturing/plating_outward')
def manufacturing_plating_outward():
    """Serve the plating outward page."""
    return serve_plating_outward_page()

@app.route('/manufacturing/assembly_receipt')
def manufacturing_assembly_receipt():
    """Serve the assembly receipt page."""
    return serve_assembly_receipt_page()

@app.route('/manufacturing/products')
def manufacturing_products():
    """Serve the products management page."""
    return serve_products_page()

@app.route('/manufacturing/api/products', methods=['GET'])
def manufacturing_get_products():
    """API endpoint to retrieve all products."""
    products = get_all_products()
    return jsonify(products)

@app.route('/manufacturing/api/products', methods=['POST'])
@app.route('/manufacturing/api/products/<product_code>', methods=['PUT'])
def update_product_api(product_code):
    """API endpoint to update an existing product."""
    try:
        # First check if the product exists
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
        SELECT * FROM product_master WHERE product_code = ?
        ''', (product_code,))

        existing_product = cursor.fetchone()
        if not existing_product:
            conn.close()
            return jsonify({'success': False, 'message': f'Product {product_code} not found'}), 404

        # Get the current photo filename
        current_photo_filename = existing_product['photo_filename']

        # Get form data
        data = request.form
        name = data.get('name')
        description = data.get('description')
        casting_weight = float(data.get('casting_weight'))
        machining_weight = float(data.get('machining_weight'))
        final_weight = float(data.get('final_weight'))

        # Handle photo upload
        photo_filename = current_photo_filename
        if 'photo' in request.files:
            photo = request.files['photo']
            if photo.filename:
                # Ensure the product_photos directory exists
                product_photos_dir = os.path.join('static', 'product_photos')
                os.makedirs(product_photos_dir, exist_ok=True)

                # Delete old photo if it exists
                if current_photo_filename and os.path.exists(os.path.join(product_photos_dir, current_photo_filename)):
                    try:
                        os.remove(os.path.join(product_photos_dir, current_photo_filename))
                    except Exception as e:
                        logger.warning(f"Failed to delete old photo {current_photo_filename}: {str(e)}")

                # Save new photo
                photo_filename = f"{product_code}_{photo.filename}"
                photo.save(os.path.join(product_photos_dir, photo_filename))

        # Update the product
        cursor.execute('''
        UPDATE product_master SET
            name = ?,
            description = ?,
            casting_weight = ?,
            machining_weight = ?,
            final_weight = ?,
            photo_filename = ?
        WHERE product_code = ?
        ''', (name, description, casting_weight, machining_weight, final_weight, photo_filename, product_code))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'Product {product_code} updated successfully',
            'product_code': product_code,
            'name': name,
            'description': description,
            'casting_weight': casting_weight,
            'machining_weight': machining_weight,
            'final_weight': final_weight,
            'photo_filename': photo_filename
        })

    except Exception as e:
        logger.error(f"Error updating product {product_code}: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 400

@app.route('/manufacturing/api/products/<product_code>', methods=['DELETE'])
def delete_product_api(product_code):
    """API endpoint to delete a product."""
    try:
        # First check if the product exists and get its photo filename
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
        SELECT photo_filename FROM product_master WHERE product_code = ?
        ''', (product_code,))

        result = cursor.fetchone()
        if not result:
            conn.close()
            return jsonify({'success': False, 'message': f'Product {product_code} not found'}), 404

        photo_filename = result['photo_filename']

        # Delete the product from the database
        cursor.execute('''
        DELETE FROM product_master WHERE product_code = ?
        ''', (product_code,))

        conn.commit()
        conn.close()

        # Delete the photo file if it exists
        product_photos_dir = os.path.join('static', 'product_photos')
        if photo_filename and os.path.exists(os.path.join(product_photos_dir, photo_filename)):
            try:
                os.remove(os.path.join(product_photos_dir, photo_filename))
            except Exception as e:
                logger.warning(f"Failed to delete photo {photo_filename}: {str(e)}")

        return jsonify({'success': True, 'message': f'Product {product_code} deleted successfully'})

    except Exception as e:
        logger.error(f"Error deleting product {product_code}: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 400

@app.route('/manufacturing/reports')
def manufacturing_reports():
    """Serve the manufacturing reports page."""
    logger.info("Serving manufacturing reports page")
    return render_template('FrontEnd/manufacturing/reports.html')

@app.route('/manufacturing/api/reports/material_loss', methods=['GET'])
def manufacturing_get_material_loss_report():
    """API endpoint to retrieve material loss report."""
    return get_material_loss_report_api()

@app.route('/manufacturing/api/reports/production_history/<product_code>', methods=['GET'])
def manufacturing_get_production_history(product_code):
    """API endpoint to retrieve production history for a product."""
    return get_production_history_api(product_code)

@app.route('/')
def index():
    """Serve the manufacturing dashboard as the main landing page.

    This is the landing page of our application - it shows an overview
    of the manufacturing process and system status.
    """
    logger.info("Serving manufacturing dashboard as main landing page")
    return serve_manufacturing_dashboard()

@app.route('/main_dashboard')
def main_dashboard():
    """Serve the original main dashboard page.

    This provides access to the original dashboard with its functionality.
    """
    logger.info("Serving original main dashboard page")
    return send_from_directory('frontend', 'dashboard.html')

@app.route('/accounts')
def serve_accounts():
    """Serve the account master page.

    This page allows users to manage client accounts, view account details,
    and update account information.
    """
    logger.info("Serving account master page")
    return send_from_directory('frontend', 'Account_Master.html')

@app.route('/tablet')
def tablet():
    """Serve the tablet management page.

    This page is optimized for tablet devices used on the factory floor.
    It provides a simplified interface for production staff.
    """
    logger.info("Serving tablet management page")
    return send_from_directory('frontend', 'tablet_management.html')
@app.route('/order_book')
def serve_order_book_page():
    # Serve order book page
    app.logger.info("Serving order book page")
    return send_from_directory('frontend', 'order_book.html')

@app.route('/client_portal')
def serve_client_portal_page():
    # Serve client portal page
    app.logger.info("Serving client portal page")
    return send_from_directory('frontend', 'client_portal.html')

@app.route('/product_names')
def serve_product_names_page():
    # Serve product names page
    app.logger.info("Serving product names page")
    return send_from_directory('frontend', 'product_names.html')

@app.route('/packing')
def serve_packing():
    """Serve the packing page.

    This page is used by the packing department to manage the packing process,
    print labels, and track packed items.
    """
    logger.info("Serving packing page")
    return send_from_directory('frontend', 'packing.html')

@app.route('/management')
def serve_management():
    """Serve the management page.

    This page provides management tools and reports for supervisors and managers.
    It includes production statistics, employee performance, and other KPIs.
    """
    logger.info("Serving management page")
    return send_from_directory('frontend', 'management.html')

@app.route('/clientnoteadd')
def serve_clientnoteadd():
    """Serve the client notes page.

    This page allows staff to add and view notes related to specific clients.
    It helps maintain a history of client interactions and special requirements.
    """
    logger.info("Serving client notes page")
    return send_from_directory('frontend', 'client_notes.html')

@app.route('/database_editor')
def serve_database_editor():
    """Serve the database editor page.

    This page provides an interface for administrators to directly edit
    database records. It should only be accessible to authorized users.
    """
    logger.info("Serving database editor page")
    return send_from_directory('frontend', 'edit.html')

@app.route('/test')
def testing():
    """Serve the test page.

    This page is used for testing new features and functionality.
    It's not part of the main application flow.
    """
    logger.info("Serving test page")
    return send_from_directory('frontend', 'test.html')

@app.route('/notes')
def serve_notes():
    """Serve the notes page.

    This page displays system-wide notes and reminders for all staff.
    It helps with internal communication and task tracking.
    """
    logger.info("Serving notes page")
    return send_from_directory('frontend', 'notes.html')


@app.route('/merge_orders_page')
def serve_merge_orders_page():
    """Serve the merge orders page.

    This page provides an interface for merging two orders together.
    It allows users to select source and target orders and combine their products.
    """
    logger.info("Serving merge orders page")
    return send_from_directory('frontend', 'merge_orders.html')

@app.route('/create_pending_order_page')
def serve_create_pending_order_page():
    """Serve the create pending order page.

    This page provides an interface for creating a new pending order based on
    the remaining products from an existing order.
    """
    logger.info("Serving create pending order page")
    return send_from_directory('frontend', 'create_pending_order.html')

@app.route('/frontend/<path:filename>')
def serve_frontend_static(filename):
    """Serve static files from the frontend folder.

    This is a catch-all route that serves any static assets (images, CSS, JavaScript)
    that are stored in the frontend folder. It's essential for the UI to work properly.

    Args:
        filename: The path to the file within the frontend folder
    """
    logger.debug(f"Serving static file: {filename}")
    return send_from_directory('frontend', filename)

#==============================================================================
#                         STATIC FOLDER ROUTE
#==============================================================================
# This route servres all the files required fpr any pages from static folder

@app.route('/static/<path:filename>')
def serve_static_folder(filename):
    """Serve static files from the static folder.

    This route handles requests for static files like images, CSS, and JavaScript.
    It's used to serve files that are not part of the main application flow.

    Args:
        filename: The name of the file to serve
    """
    return send_from_directory(app.static_folder, filename)

#==============================================================================
#                         API ROUTES
#==============================================================================
# These routes provide data to the frontend via JSON API endpoints
# They handle data retrieval, processing, and database operations

@app.route('/get_client_data', methods=['GET'])
def get_client_data():
    """Get all client data from the database.

    This endpoint retrieves all client records from the database and returns them
    as a JSON array. It's used by the Account Master page to display client information.

    Returns:
        JSON: Array of client objects with all their properties
    """
    logger.info("API request received: get_client_data")

    # Call our data access function to fetch the client data
    data = fetch_data_from_sqlite(db_name="client_db.db", table_name="clients")

    # Log the result and return the data as JSON
    logger.info(f"Returning {len(data)} client records")
    return jsonify(data)

@app.route('/client_orders/<client_id>', methods=['GET'])
def get_client_orders(client_id):
    """Get recent orders for a specific client with product details and totals.

    This endpoint retrieves the 5 most recent orders for the specified client,
    including total product counts, quantities, and packed status.

    Args:
        client_id (str): The client ID to get orders for

    Returns:
        JSON: Array of recent orders with calculated totals
    """
    logger.info(f"API request received: get_client_orders for client {client_id}")

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Query to get orders with product details and totals
        cursor.execute("""
            WITH OrderTotals AS (
                SELECT
                    od.order_id,
                    COUNT(DISTINCT od.product_code) as total_products,
                    SUM(od.quantity_pcs) as total_quantity_pcs,
                    SUM(od.quantity_sets) as total_quantity_sets,
                    SUM(od.packed_pcs) as total_packed_pcs,
                    SUM(od.packed_sets) as total_packed_sets
                FROM order_details od
                GROUP BY od.order_id
            )
            SELECT
                o.order_id,
                o.client_id,
                o.order_date,
                o.status,
                o.line,
                o.scheduled_date,
                ot.total_products,
                ot.total_quantity_pcs,
                ot.total_quantity_sets,
                ot.total_packed_pcs,
                ot.total_packed_sets
            FROM orders o
            LEFT JOIN OrderTotals ot ON o.order_id = ot.order_id
            WHERE o.client_id = ?
            ORDER BY o.order_date DESC
            LIMIT 5
        """, (client_id,))

        # Fetch results
        orders = cursor.fetchall()

        # Format the results
        formatted_orders = []
        for order in orders:
            # Calculate total quantities (1 SET = 2 PCS for backward compatibility)
            total_quantity = (order[7] or 0) + ((order[8] or 0) * 2)
            total_packed = (order[9] or 0) + ((order[10] or 0) * 2)

            formatted_orders.append({
                'orderId': order[0],
                'clientId': order[1],
                'orderDate': order[2],
                'status': order[3],
                'line': order[4],
                'scheduledDate': order[5],
                'totalProducts': order[6] or 0,  # Handle NULL values
                'totalQuantityPcs': order[7] or 0,
                'totalQuantitySets': order[8] or 0,
                'totalPackedPcs': order[9] or 0,
                'totalPackedSets': order[10] or 0,
                # For backward compatibility
                'totalQuantity': total_quantity,
                'totalPacked': total_packed,
                'completionPercentage': round((total_packed / total_quantity) * 100) if total_quantity > 0 else 0
            })

        logger.info(f"Returning {len(formatted_orders)} orders for client {client_id}")
        return jsonify(formatted_orders)

    except sqlite3.Error as e:
        logger.error(f"Database error in get_client_orders: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(e)}"
        }), 500

    except Exception as e:
        logger.error(f"Unexpected error in get_client_orders: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"An unexpected error occurred: {str(e)}"
        }), 500

    finally:
        conn.close()
#=============================================================================
#                         CLIENT MANAGEMENT API FUNCTIONS
#=============================================================================
# These functions handle the client management functionality

def get_client_db_connection():
    """Get a connection to the client database.

    Returns:
        sqlite3.Connection: A connection to the client database
    """
    return get_db_connection(db_name="client_db.db")


#=============================================================================
#                         CLIENT MANAGEMENT API ROUTES
#=============================================================================
# These routes handle the client management functionality

@app.route('/api/clients', methods=['GET'])
def get_all_clients():
    """Retrieve all client data from the SQLite database."""
    conn = None
    try:
        # Establish connection to the SQLite database
        conn = sqlite3.connect('client_db.db')
        cursor = conn.cursor()

        # Query to fetch all client data with all columns
        query = 'SELECT * FROM clients'
        cursor.execute(query)
        rows = cursor.fetchall()

        # Get column names
        columns = [description[0] for description in cursor.description]

        # Convert rows into a list of dictionaries with all fields
        clients = []
        for row in rows:
            client_dict = dict(zip(columns, row))
            # Ensure these key names match what the frontend expects
            if 'ClientID' in client_dict:
                client_dict['clientId'] = client_dict['ClientID']
            if 'PartyName' in client_dict:
                client_dict['partyName'] = client_dict['PartyName']
            clients.append(client_dict)

        return jsonify({"status": "success", "data": clients})

    except sqlite3.Error as db_error:
        app.logger.error(f"Database error: {db_error}")
        return jsonify({"status": "error", "message": "Database error occurred."}), 500

    except Exception as e:
        app.logger.error(f"Unexpected error: {e}")
        return jsonify({"status": "error", "message": "An unexpected error occurred."}), 500

    finally:
        # Ensure the database connection is closed
        if conn:
            conn.close()
@app.route('/today_orders_by_client/<client_id>')
def get_today_orders_by_client(client_id):
    conn = sqlite3.connect('Trione_ERP_Database.db')
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # Get all orders for the client that are marked for today
    c.execute('''
        SELECT o.order_id, o.client_id, o.order_date, o.status, o.is_today, o.line, o.packed_date, o.scheduled_date
        FROM orders o
        WHERE o.client_id = ? AND o.is_today = 1
    ''', (client_id,))

    orders = c.fetchall()

    result = []
    for order in orders:
        order_dict = dict(order)

        c.execute('''
            SELECT od.product_code, od.quantity_to_make,
                   COALESCE(SUM(pp.quantity_packed), 0) as packed
            FROM order_details od
            LEFT JOIN packed_products pp ON od.order_id = pp.order_id AND od.product_code = pp.product_code
            WHERE od.order_id = ?
            GROUP BY od.product_code, od.quantity_to_make
        ''', (order['order_id'],))

        items = c.fetchall()
        order_dict['items'] = [dict(item) for item in items if item['packed'] > 0]

        if order_dict['items']:
            result.append(order_dict)

    conn.close()

    return jsonify(result)
#==============================================================================
#                         NOTES API ROUTES
#==============================================================================
# These routes handle the system-wide notes functionality
# Notes can be used for internal communication and task tracking
# To be added as soon as possible only if needed

#==============================================================================
#                         PRODUCT NAMES API ROUTES
#==============================================================================
# These routes handle product name-related operations

@app.route('/api/product-names', methods=['GET'])
def get_all_product_names():
    """Get all product names from the database.

    Returns:
        JSON: A list of all product names with their codes
    """
    conn = get_db_connection()
    c = conn.cursor()
    c.execute("SELECT product_code, product_name FROM product_names ORDER BY product_code")
    products = [{'code': row[0], 'name': row[1]} for row in c.fetchall()]
    conn.close()

    return jsonify({
        'status': 'success',
        'count': len(products),
        'products': products
    })

@app.route('/api/product-name/<product_code>', methods=['GET'])
def get_product_name_api(product_code):
    """Get the product name for a specific product code.

    Args:
        product_code (str): The product code to look up

    Returns:
        JSON: The product name for the given code
    """
    name = get_product_name(product_code)

    return jsonify({
        'status': 'success',
        'code': product_code,
        'name': name
    })

@app.route('/api/product-names', methods=['POST'])
def add_product_name():
    """Add a new product name to the database.

    Request body should contain:
    - code: The product code
    - name: The product name

    Returns:
        JSON: Success or error message
    """
    data = request.json

    if not data or 'code' not in data or 'name' not in data:
        return jsonify({
            'status': 'error',
            'message': 'Product code and name are required'
        }), 400

    product_code = data['code'].strip()
    product_name = data['name'].strip()

    if not product_code or not product_name:
        return jsonify({
            'status': 'error',
            'message': 'Product code and name cannot be empty'
        }), 400

    conn = get_db_connection()
    c = conn.cursor()

    try:
        # Check if product code already exists
        c.execute("SELECT COUNT(*) FROM product_names WHERE product_code = ?", (product_code,))
        if c.fetchone()[0] > 0:
            conn.close()
            return jsonify({
                'status': 'error',
                'message': f'Product code {product_code} already exists'
            }), 400

        # Insert the new product name
        c.execute("INSERT INTO product_names (product_code, product_name) VALUES (?, ?)",
                 (product_code, product_name))
        conn.commit()

        return jsonify({
            'status': 'success',
            'message': f'Product {product_code} - {product_name} added successfully',
            'product': {
                'code': product_code,
                'name': product_name
            }
        })

    except Exception as e:
        conn.rollback()
        logger.error(f"Error adding product name: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'Database error: {str(e)}'
        }), 500

    finally:
        conn.close()


#==============================================================================
#                         MANAGEMENT DASHBOARD ROUTES
#==============================================================================
# These routes handle the management dashboard functionality and reporting



@app.route('/orders/<timeframe>')
def get_orders_by_timeframe(timeframe):
    """Get orders filtered by timeframe.

    This endpoint retrieves orders based on the specified timeframe (today, week, month, all).
    It also calculates total quantities for each order.

    Args:
        timeframe (str): The timeframe to filter orders by (today, week, month, all)

    Returns:
        JSON: Array of order objects with calculated totals
    """
    logger.info(f"API request received: get_orders_by_timeframe for {timeframe}")

    try:
        # First check if the order_details table has the new quantity columns
        conn = get_db_connection()
        c = conn.cursor()

        # Check if the quantity_pcs column exists in order_details
        c.execute("PRAGMA table_info(order_details)")
        columns = [column[1] for column in c.fetchall()]

        has_quantity_columns = 'quantity_pcs' in columns and 'quantity_sets' in columns
        has_packed_columns = 'packed_pcs' in columns and 'packed_sets' in columns

        logger.info(f"Database check: has_quantity_columns={has_quantity_columns}, has_packed_columns={has_packed_columns}")

        # Build the query based on available columns
        if has_quantity_columns and has_packed_columns:
            # Use the new columns
            quantity_select = """
                COUNT(DISTINCT od.product_code) as total_products,
                SUM(od.quantity_pcs) as total_quantity_pcs,
                SUM(od.quantity_sets) as total_quantity_sets,
                SUM(od.packed_pcs) as total_packed_pcs,
                SUM(od.packed_sets) as total_packed_sets
            """
        else:
            # Fallback to using just the product count
            quantity_select = """
                COUNT(DISTINCT od.product_code) as total_products,
                0 as total_quantity_pcs,
                0 as total_quantity_sets,
                0 as total_packed_pcs,
                0 as total_packed_sets
            """

        # Base query
        base_query = f"""
            SELECT o.order_id, o.client_id, o.status, o.order_date, o.is_today, o.line, o.packed_date, o.scheduled_date,
                   {quantity_select}
            FROM orders o
            LEFT JOIN order_details od ON o.order_id = od.order_id
        """

        # Add the WHERE clause based on timeframe
        if timeframe == 'today':
            c.execute(base_query + """
                WHERE o.is_today = 1
                GROUP BY o.order_id, o.client_id, o.status, o.order_date, o.is_today, o.line, o.packed_date, o.scheduled_date
            """)
        elif timeframe == 'week':
            today = datetime.now().date()
            week_later = today + timedelta(days=7)
            c.execute(base_query + """
                WHERE o.scheduled_date BETWEEN ? AND ?
                GROUP BY o.order_id, o.client_id, o.status, o.order_date, o.is_today, o.line, o.packed_date, o.scheduled_date
            """, (today.strftime('%Y-%m-%d'), week_later.strftime('%Y-%m-%d')))
        elif timeframe == 'month':
            today = datetime.now().date()
            month_later = today + timedelta(days=30)
            c.execute(base_query + """
                WHERE o.scheduled_date BETWEEN ? AND ?
                GROUP BY o.order_id, o.client_id, o.status, o.order_date, o.is_today, o.line, o.packed_date, o.scheduled_date
            """, (today.strftime('%Y-%m-%d'), month_later.strftime('%Y-%m-%d')))
        else:  # 'all' or any other value
            c.execute(base_query + """
                GROUP BY o.order_id, o.client_id, o.status, o.order_date, o.is_today, o.line, o.packed_date, o.scheduled_date
            """)
        # Process the results
        orders = [{
            "orderId": row[0],
            "clientId": row[1],
            "status": row[2],
            "orderDate": row[3],
            "is_today": bool(row[4]),
            "line": row[5],
            "packedDate": row[6],
            "scheduledDate": row[7],
            "totalProducts": row[8],
            "totalQuantityPcs": row[9] or 0,
            "totalQuantitySets": row[10] or 0,
            "totalPackedPcs": row[11] or 0,
            "totalPackedSets": row[12] or 0,
            # Calculate total quantities for backward compatibility
            "totalQuantity": (row[9] or 0) + ((row[10] or 0) * 2),  # 1 SET = 2 PCS
            "totalPacked": (row[11] or 0) + ((row[12] or 0) * 2)   # 1 SET = 2 PCS
        } for row in c.fetchall()]

        conn.close()
        logger.info(f"Returning {len(orders)} orders for timeframe '{timeframe}'")
        return jsonify(orders)

    except sqlite3.Error as e:
        logger.error(f"Database error in get_orders_by_timeframe: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(e)}"
        }), 500

    except Exception as e:
        logger.error(f"Unexpected error in get_orders_by_timeframe: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"An unexpected error occurred: {str(e)}"
        }), 500

# End of get_orders_by_timeframe function



#==============================================================================
#                         ORDER MANAGEMENT ROUTES
#==============================================================================
# These routes handle order creation, management, and processing

@app.route('/merge_orders', methods=['POST'])
def merge_orders():
    """Merge two orders into one.

    This endpoint combines the products from an old order into a new order,
    then deletes the old order. It handles both PCS and SETS quantities,
    properly combining them for products that exist in both orders.

    Request body should contain:
    - oldOrderId: The ID of the order to merge from (will be deleted)
    - newOrderId: The ID of the order to merge into (will be kept)

    Returns:
        JSON: Success or error message
    """
    logger.info("API request received: merge_orders")

    data = request.json
    # Extract the old and new order ids from the request
    old_order_id = data.get('oldOrderId')
    new_order_id = data.get('newOrderId')

    # Validate the provided order IDs
    if old_order_id is None or new_order_id is None:
        logger.warning("Missing required parameters in merge_orders request")
        return jsonify({"status": "error", "message": "Both oldOrderId and newOrderId are required."}), 400

    # Ensure we're not trying to merge an order with itself
    if old_order_id == new_order_id:
        logger.warning(f"Attempted to merge order {old_order_id} with itself")
        return jsonify({"status": "error", "message": "Cannot merge an order with itself."}), 400

    conn = get_db_connection()
    c = conn.cursor()

    try:
        # First verify both orders exist
        c.execute("SELECT client_id FROM orders WHERE order_id = ?", (old_order_id,))
        old_order = c.fetchone()

        c.execute("SELECT client_id FROM orders WHERE order_id = ?", (new_order_id,))
        new_order = c.fetchone()

        if not old_order:
            logger.warning(f"Old order {old_order_id} not found")
            return jsonify({"status": "error", "message": f"Order {old_order_id} not found."}), 404

        if not new_order:
            logger.warning(f"New order {new_order_id} not found")
            return jsonify({"status": "error", "message": f"Order {new_order_id} not found."}), 404

        # Check if orders belong to the same client
        if old_order[0] != new_order[0]:
            logger.warning(f"Cannot merge orders from different clients: {old_order[0]} and {new_order[0]}")
            return jsonify({"status": "error", "message": "Cannot merge orders from different clients."}), 400

        # Retrieve all products for the old order
        c.execute("""
            SELECT product_code, quantity_pcs, quantity_sets, packed_pcs, packed_sets
            FROM order_details
            WHERE order_id = ?
        """, (old_order_id,))
        old_order_products = c.fetchall()

        # Merge each product into the new order
        for product in old_order_products:
            product_code = product[0]
            quantity_pcs = product[1]
            quantity_sets = product[2]
            packed_pcs = product[3]
            packed_sets = product[4]

            # Check if this product already exists in the new order
            c.execute("""
                SELECT id, quantity_pcs, quantity_sets, packed_pcs, packed_sets
                FROM order_details
                WHERE order_id = ? AND product_code = ?
            """, (new_order_id, product_code))
            result = c.fetchone()

            if result:
                # If it exists, update the quantities
                detail_id = result[0]
                existing_qty_pcs = result[1]
                existing_qty_sets = result[2]
                existing_packed_pcs = result[3]
                existing_packed_sets = result[4]

                updated_qty_pcs = existing_qty_pcs + quantity_pcs
                updated_qty_sets = existing_qty_sets + quantity_sets
                updated_packed_pcs = existing_packed_pcs + packed_pcs
                updated_packed_sets = existing_packed_sets + packed_sets

                c.execute("""
                    UPDATE order_details
                    SET quantity_pcs = ?, quantity_sets = ?, packed_pcs = ?, packed_sets = ?
                    WHERE id = ?
                """, (updated_qty_pcs, updated_qty_sets, updated_packed_pcs, updated_packed_sets, detail_id))
            else:
                # Otherwise, insert the product as a new record for the new order
                c.execute("""
                    INSERT INTO order_details
                    (order_id, product_code, quantity_pcs, quantity_sets, packed_pcs, packed_sets)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (new_order_id, product_code, quantity_pcs, quantity_sets, packed_pcs, packed_sets))

        # Also merge any packed products from the old order
        c.execute("""
            SELECT product_code, carton_number, quantity_packed_pcs, quantity_packed_sets
            FROM packed_products
            WHERE order_id = ?
        """, (old_order_id,))
        old_packed_products = c.fetchall()

        # Get the highest carton number in the new order to avoid conflicts
        c.execute("SELECT MAX(carton_number) FROM packed_products WHERE order_id = ?", (new_order_id,))
        max_carton = c.fetchone()[0]
        next_carton = (max_carton or 0) + 1

        # Merge packed products with new carton numbers
        for packed in old_packed_products:
            product_code = packed[0]
            # Use a new carton number to avoid conflicts
            quantity_packed_pcs = packed[2]
            quantity_packed_sets = packed[3]

            c.execute("""
                INSERT INTO packed_products
                (order_id, product_code, carton_number, quantity_packed_pcs, quantity_packed_sets)
                VALUES (?, ?, ?, ?, ?)
            """, (new_order_id, product_code, next_carton, quantity_packed_pcs, quantity_packed_sets))

            # Also transfer any carton weights
            c.execute("""
                SELECT weight FROM carton_weights
                WHERE order_id = ? AND product_code = ? AND carton_number = ?
            """, (old_order_id, product_code, packed[1]))
            weight_result = c.fetchone()

            if weight_result:
                c.execute("""
                    INSERT INTO carton_weights
                    (order_id, product_code, carton_number, weight)
                    VALUES (?, ?, ?, ?)
                """, (new_order_id, product_code, next_carton, weight_result[0]))

            next_carton += 1

        # Delete the old order from the orders table
        c.execute("DELETE FROM orders WHERE order_id = ?", (old_order_id,))

        # Delete the old order details from the order_details table
        c.execute("DELETE FROM order_details WHERE order_id = ?", (old_order_id,))

        # Delete the old packed products
        c.execute("DELETE FROM packed_products WHERE order_id = ?", (old_order_id,))

        # Delete the old carton weights
        c.execute("DELETE FROM carton_weights WHERE order_id = ?", (old_order_id,))

        # Update Excel file for the new order
        try:
            # Get client ID
            client_id = new_order[0]

            # Get all products for the new order
            c.execute("""
                SELECT product_code, quantity_pcs, quantity_sets
                FROM order_details
                WHERE order_id = ?
            """, (new_order_id,))
            products = c.fetchall()

            # Prepare Excel product details
            excel_products = []
            for product in products:
                product_code = product[0]
                quantity_pcs = product[1]
                quantity_sets = product[2]

                # Calculate total quantity for Excel
                total_quantity = quantity_pcs + (quantity_sets * 2)  # 1 SET = 2 PCS
                excel_products.append({
                    'productCode': product_code,
                    'quantityToMake': total_quantity,
                    'quantityPcs': quantity_pcs,
                    'quantitySets': quantity_sets
                })

            # Define orders directory
            orders_dir = 'Orders-new'
            os.makedirs(orders_dir, exist_ok=True)

            # Update Excel file
            excel_file_path = os.path.join(orders_dir, f'{client_id}_pending_orders.xlsx')

            # Create or update Excel file
            if os.path.exists(excel_file_path):
                # Read existing Excel file
                df = pd.read_excel(excel_file_path)

                # Remove existing entries for this order
                df = df[df['OrderID'] != new_order_id]
            else:
                # Create new DataFrame with columns
                df = pd.DataFrame(columns=['OrderID', 'ProductCode', 'QuantityToMake', 'QuantityPcs', 'QuantitySets'])

            # Add new entries
            for product in excel_products:
                new_row = {
                    'OrderID': new_order_id,
                    'ProductCode': product['productCode'],
                    'QuantityToMake': product['quantityToMake'],
                    'QuantityPcs': product['quantityPcs'],
                    'QuantitySets': product['quantitySets']
                }
                df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)

            # Save Excel file
            df.to_excel(excel_file_path, index=False)
            logger.info(f"Excel file updated successfully: {excel_file_path}")

        except Exception as excel_error:
            logger.error(f"Excel file update failed: {str(excel_error)}")
            # Log error but don't interrupt the merge process

        conn.commit()
        logger.info(f"Successfully merged order {old_order_id} into {new_order_id}")
        return jsonify({"status": "success", "message": f"Order {old_order_id} successfully merged into order {new_order_id}"})

    except sqlite3.Error as db_error:
        conn.rollback()
        logger.error(f"Database error in merge_orders: {str(db_error)}")
        return jsonify({"status": "error", "message": f"Database error: {str(db_error)}"}), 500

    except Exception as e:
        conn.rollback()
        logger.error(f"Unexpected error in merge_orders: {str(e)}")
        return jsonify({"status": "error", "message": f"An error occurred: {str(e)}"}), 500

    finally:
        conn.close()
@app.route('/create_pending_order', methods=['POST'])
@app.route('/update_carton', methods=['POST'])
def update_carton():
    logger.info("API request received: update_carton")
    data = request.get_json()

    if not data or 'order_id' not in data or 'carton_number' not in data or 'products' not in data:
        logger.error("Missing required parameters in update_carton (order_id, carton_number, products)")
        return jsonify({'status': 'error', 'message': 'order_id, carton_number, and products are required.'}), 400

    try:
        order_id = int(data['order_id'])
        carton_number = int(data['carton_number'])
        new_products_in_carton_request = data.get('products', [])
        if not isinstance(new_products_in_carton_request, list):
            logger.error("Invalid format for products, expected a list.")
            return jsonify({'status': 'error', 'message': 'Products must be a list.'}), 400
        
        weight_str = data.get('weight') # Weight is optional

    except ValueError:
        logger.error("Invalid order_id or carton_number format.")
        return jsonify({'status': 'error', 'message': 'order_id and carton_number must be integers.'}), 400

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        conn.execute("BEGIN TRANSACTION")

        # 1. Fetch current products in the carton to calculate deltas later
        cursor.execute("""
            SELECT product_code, quantity_packed_pcs, quantity_packed_sets
            FROM packed_products
            WHERE order_id = ? AND carton_number = ?
        """, (order_id, carton_number))
        old_carton_items_list = cursor.fetchall()
        
        old_packed_quantities_map = {}
        for item_row in old_carton_items_list:
            old_packed_quantities_map[item_row[0]] = {'pcs': item_row[1], 'sets': item_row[2]}

        # 2. Calculate net changes for order_details based on old and new carton contents
        product_deltas = {} # {product_code: {pcs: delta, sets: delta}}

        # Subtract old quantities
        for product_code, quantities in old_packed_quantities_map.items():
            product_deltas.setdefault(product_code, {'pcs': 0, 'sets': 0})
            product_deltas[product_code]['pcs'] -= quantities['pcs']
            product_deltas[product_code]['sets'] -= quantities['sets']

        # Add new quantities
        valid_new_products_for_carton = []
        for prod_data in new_products_in_carton_request:
            if not isinstance(prod_data, dict) or 'product_code' not in prod_data:
                conn.rollback()
                logger.error(f"Invalid product data format in request: {prod_data}")
                return jsonify({'status': 'error', 'message': 'Each product must be an object with product_code.'}), 400

            p_code = prod_data['product_code']
            try:
                p_qty_pcs = int(prod_data.get('quantity_pcs', 0))
                p_qty_sets = int(prod_data.get('quantity_sets', 0))
            except ValueError:
                conn.rollback()
                logger.error(f"Invalid quantity for product {p_code}")
                return jsonify({'status': 'error', 'message': f'Invalid quantity for product {p_code}. Must be integer.'}), 400

            if p_qty_pcs < 0 or p_qty_sets < 0:
                conn.rollback()
                logger.error(f"Negative quantity for product {p_code}")
                return jsonify({'status': 'error', 'message': f'Quantities cannot be negative for product {p_code}.'}), 400
            
            # Only consider products with actual quantities for insertion and delta calculation
            if p_qty_pcs > 0 or p_qty_sets > 0:
                valid_new_products_for_carton.append({
                    'product_code': p_code,
                    'quantity_pcs': p_qty_pcs,
                    'quantity_sets': p_qty_sets
                })
                product_deltas.setdefault(p_code, {'pcs': 0, 'sets': 0})
                product_deltas[p_code]['pcs'] += p_qty_pcs
                product_deltas[p_code]['sets'] += p_qty_sets
        
        # 3. Validate and update order_details based on calculated deltas
        for product_code, deltas in product_deltas.items():
            if deltas['pcs'] == 0 and deltas['sets'] == 0:
                continue

            cursor.execute("""
                SELECT quantity_pcs, quantity_sets, packed_pcs, packed_sets
                FROM order_details
                WHERE order_id = ? AND product_code = ?
            """, (order_id, product_code))
            od_row = cursor.fetchone()

            if not od_row:
                conn.rollback()
                logger.error(f"Product {product_code} not found in order_details for order {order_id}.")
                return jsonify({'status': 'error', 'message': f"Product {product_code} not in order details."}), 404

            od_total_pcs, od_total_sets, od_current_packed_pcs, od_current_packed_sets = od_row
            
            new_od_packed_pcs = od_current_packed_pcs + deltas['pcs']
            new_od_packed_sets = od_current_packed_sets + deltas['sets']

            if not (0 <= new_od_packed_pcs <= od_total_pcs):
                conn.rollback()
                logger.error(f"Invalid packed PCS for {product_code} (Order: {od_total_pcs}, Attempted: {new_od_packed_pcs})")
                return jsonify({'status': 'error', 'message': f"Packed PCS for {product_code} would be {new_od_packed_pcs}, but order quantity is {od_total_pcs} and cannot be less than 0."}), 400
            
            if not (0 <= new_od_packed_sets <= od_total_sets):
                conn.rollback()
                logger.error(f"Invalid packed SETS for {product_code} (Order: {od_total_sets}, Attempted: {new_od_packed_sets})")
                return jsonify({'status': 'error', 'message': f"Packed SETS for {product_code} would be {new_od_packed_sets}, but order quantity is {od_total_sets} and cannot be less than 0."}), 400

            cursor.execute("""
                UPDATE order_details
                SET packed_pcs = ?, packed_sets = ?
                WHERE order_id = ? AND product_code = ?
            """, (new_od_packed_pcs, new_od_packed_sets, order_id, product_code))

        # 4. Update packed_products table: clear carton and insert new valid products
        cursor.execute("DELETE FROM packed_products WHERE order_id = ? AND carton_number = ?", (order_id, carton_number))
        
        for prod_to_insert in valid_new_products_for_carton:
            cursor.execute("""
                INSERT INTO packed_products (order_id, carton_number, product_code, quantity_packed_pcs, quantity_packed_sets)
                VALUES (?, ?, ?, ?, ?)
            """, (order_id, carton_number, prod_to_insert['product_code'], prod_to_insert['quantity_pcs'], prod_to_insert['quantity_sets']))

        # 5. Update carton_weights
        carton_weight_to_db = None
        if weight_str is not None and str(weight_str).strip() != "":
            try:
                parsed_weight = float(weight_str)
                if parsed_weight > 0:
                    carton_weight_to_db = parsed_weight
                # If parsed_weight is 0 or negative, it will be treated as delete/None later
            except ValueError:
                conn.rollback()
                logger.error(f"Invalid weight format: {weight_str}")
                return jsonify({'status': 'error', 'message': 'Invalid weight format. Must be a number.'}), 400
        
        if carton_weight_to_db is not None and valid_new_products_for_carton:
            # Weight is positive and carton is not empty, upsert weight
            # Use the product_code of the first item in the now-updated carton for carton_weights table FK
            product_code_for_weight_table = valid_new_products_for_carton[0]['product_code']
            
            # Ensure this product_code actually exists in order_details (should be guaranteed by earlier checks)
            cursor.execute("SELECT 1 FROM order_details WHERE order_id = ? AND product_code = ?", (order_id, product_code_for_weight_table))
            if not cursor.fetchone():
                # This should ideally not happen if previous logic is correct
                conn.rollback()
                logger.error(f"Consistency error: Product {product_code_for_weight_table} for weight table not found in order_details for order {order_id}")
                return jsonify({'status': 'error', 'message': 'Internal server error: Could not verify product for weight entry.'}), 500

            cursor.execute("""
                INSERT OR REPLACE INTO carton_weights (order_id, carton_number, product_code, weight)
                VALUES (?, ?, ?, ?)
            """, (order_id, carton_number, product_code_for_weight_table, carton_weight_to_db))
            logger.info(f"Upserted weight for carton {carton_number} order {order_id} to {carton_weight_to_db} kg using product {product_code_for_weight_table}.")
        else:
            # Carton is empty OR weight is null/zero/negative, so delete existing weight entry
            cursor.execute("DELETE FROM carton_weights WHERE order_id = ? AND carton_number = ?", (order_id, carton_number))
            if not valid_new_products_for_carton:
                 logger.info(f"Carton {carton_number} order {order_id} is now empty. Weight entry removed.")
            else:
                 logger.info(f"Weight for carton {carton_number} order {order_id} is null or non-positive. Weight entry removed.")

        conn.commit()
        logger.info(f"Carton {carton_number} for order {order_id} updated successfully.")
        return jsonify({'status': 'success', 'message': 'Carton updated successfully'})

    except sqlite3.Error as e:
        if conn:
            conn.rollback()
        logger.error(f"Database error in update_carton: {str(e)}")
        return jsonify({'status': 'error', 'message': f"Database error: {str(e)}"}), 500
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Unexpected error in update_carton: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': f"An unexpected error occurred: {str(e)}"}), 500
    finally:
        if conn:
            conn.close()
@app.route('/update_order_details', methods=['POST'])
def update_order_details():
    """Update order details with new quantities for both PCS and SETS.

    This endpoint updates the quantities of products in an order.
    It handles both PCS and SETS quantities, updating both the database
    and optionally generating a new Excel file if a clientId is provided.

    Request body should contain:
    - orderId: The ID of the order to update
    - products: Array of product details with quantities and unit types

    Returns:
        JSON: Success or error message
    """
    logger.info("API request received: update_order_details")
    
    # Get the request data
    data = request.get_json()
    
    # Check for required parameters
    if 'orderId' not in data:
        logger.error("Missing required parameter: orderId")
        return jsonify({"status": "error", "message": "Missing required parameter: orderId"}), 400
    
    if 'products' not in data:
        logger.error("Missing required parameter: products")
        return jsonify({"status": "error", "message": "Missing required parameter: products"}), 400
        
    order_id = data['orderId']
    products = data['products']
    client_id = data.get('clientId')  # Optional parameter
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # First verify the order exists
        cursor.execute("SELECT COUNT(*) FROM orders WHERE order_id = ?", (order_id,))
        if cursor.fetchone()[0] == 0:
            logger.warning(f"Order {order_id} not found")
            conn.close()
            return jsonify({"status": "error", "message": f"Order {order_id} not found"}), 404
        
        # Delete the existing order details
        cursor.execute("DELETE FROM order_details WHERE order_id = ?", (order_id,))
        
        # Insert the new order details
        insert_data = []
        
        for product in products:
            product_code = product.get('productCode')
            
            # Check for different naming conventions and formats
            # Handle both camelCase (frontend) and snake_case (API) formats
            quantity_pcs = product.get('quantity_pcs', product.get('quantityPcs', 0))
            quantity_sets = product.get('quantity_sets', product.get('quantitySets', 0))
            packed_pcs = product.get('packed_pcs', product.get('packedPcs', 0))
            packed_sets = product.get('packed_sets', product.get('packedSets', 0))
            
            # For backward compatibility - if only quantityToMake is provided
            if 'quantityToMake' in product and quantity_pcs == 0 and quantity_sets == 0:
                quantity_pcs = product.get('quantityToMake', 0)
                
            # For backward compatibility - if only packed is provided
            if 'packed' in product and packed_pcs == 0 and packed_sets == 0:
                packed_pcs = product.get('packed', 0)
            
            # Insert into database
            cursor.execute(
                """
                INSERT INTO order_details 
                (order_id, product_code, quantity_pcs, quantity_sets, packed_pcs, packed_sets) 
                VALUES (?, ?, ?, ?, ?, ?)
                """, 
                (order_id, product_code, quantity_pcs, quantity_sets, packed_pcs, packed_sets)
            )
            
            # Prepare data for Excel update
            insert_data.append({
                'productCode': product_code,
                'quantityPcs': quantity_pcs,
                'quantitySets': quantity_sets,
                'quantity': quantity_pcs + (quantity_sets * 2),  # For backward compatibility
                'packedPcs': packed_pcs,
                'packedSets': packed_sets,
                'packed': packed_pcs + (packed_sets * 2)  # For backward compatibility
            })
        
        conn.commit()
        logger.info(f"Successfully updated {len(products)} products for order {order_id}")
        
        # If a clientId was provided, generate a new Excel file
        if client_id:
            logger.info(f"Generating Excel file for client {client_id}, order {order_id}")
            try:
                generate_po_helper(client_id, insert_data, order_id)
                logger.info("Excel file generated successfully")
            except Exception as e:
                logger.error(f"Error generating Excel file: {str(e)}")
                # Continue even if Excel generation fails
        
        return jsonify({
            "status": "success",
            "message": f"Successfully updated products for order {order_id}"
        })
        
    except sqlite3.Error as e:
        conn.rollback()
        logger.error(f"Database error in update_order_details: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(e)}"
        }), 500
        
    except Exception as e:
        conn.rollback()
        logger.error(f"Unexpected error in update_order_details: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Unexpected error: {str(e)}"
        }), 500
        
    finally:
        conn.close()

@app.route('/clear_order', methods=['POST'])
@cross_origin(origin='*', headers=['Content-Type', 'Authorization'])
@app.route('/add_order', methods=['POST'])
@cross_origin(origin='*', headers=['Content-Type', 'Authorization'])
def add_order():
    """Create a new order with product details, supporting both PCS and SETS quantities.

    This endpoint creates a new order with the provided details. It supports both PCS and
    SETS quantities for each product. It also generates an Excel file with the order details
    if successful.

    Request body should contain:
    - clientId: The client ID for whom the order is being created
    - orderDate: The date of the order (optional, defaults to current date)
    - status: The status of the order (optional, defaults to "pending")
    - scheduledDate: The scheduled date for the order (optional)
    - products: An array of products with their quantities

    Returns:
        JSON: Success message with the new order ID, or error message
    """
    logger.info("API request received: add_order")

    # Get the request data
    data = request.get_json()
    if not data:
        logger.error("No data provided in create_order request")
        return jsonify({"status": "error", "message": "Request body is required"}), 400

    # Check for required parameters
    if 'clientId' not in data:
        logger.error("Missing required parameter: clientId")
        return jsonify({"status": "error", "message": "clientId is required"}), 400

    if 'products' not in data or not data['products']:
        logger.error("Missing required parameter: products or empty products array")
        return jsonify({"status": "error", "message": "products array is required and must not be empty"}), 400

    client_id = data['clientId']
    # Ensure this is from datetime import datetime, date or similar for strptime and date()
    order_date_str = data.get('orderDate', datetime.now().strftime('%Y-%m-%d'))
    status = data.get('status', 'pending')
    scheduled_date = data.get('scheduledDate')
    products = data['products']

    # Determine if the order is for today
    try:
        # Assuming datetime object is available (e.g., from datetime import datetime)
        parsed_order_date = datetime.strptime(order_date_str, '%Y-%m-%d').date()
        current_date = datetime.now().date()
        today_order_value = 1 if parsed_order_date == current_date else 0
    except ValueError:
        logger.error(f"Invalid orderDate format: {order_date_str}. Defaulting today_order to 0.")
        today_order_value = 0
    except Exception as e: # Catch any other unexpected errors during date processing
        logger.error(f"Error processing orderDate for today_order logic: {e}. Defaulting today_order to 0.")
        today_order_value = 0

    # Create a connection to the database
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Generate a new order ID
        cursor.execute("SELECT MAX(order_id) FROM orders")
        result = cursor.fetchone()
        new_order_id = 1 if result[0] is None else result[0] + 1

        # Insert the new order
        cursor.execute("""
            INSERT INTO orders (order_id, client_id, order_date, status, scheduled_date, today_order)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (new_order_id, client_id, order_date_str, status, scheduled_date, today_order_value))

        # Insert the order details
        products_for_excel = []

        for product in products:
            product_code = product.get('productCode')
            
            # Handle both camelCase and snake_case naming conventions
            quantity_pcs = product.get('quantity_pcs', product.get('quantityPcs', 0))
            quantity_sets = product.get('quantity_sets', product.get('quantitySets', 0))
            
            # For backward compatibility - if only quantityToMake is provided
            if 'quantityToMake' in product and quantity_pcs == 0 and quantity_sets == 0:
                quantity_pcs = product.get('quantityToMake', 0)
            
            # Skip products with zero quantities
            if quantity_pcs == 0 and quantity_sets == 0:
                continue
                
            # Insert into database - always use 0 for packed quantities for a new order
            cursor.execute("""
                INSERT INTO order_details (order_id, product_code, quantity_pcs, quantity_sets, packed_pcs, packed_sets)
                VALUES (?, ?, ?, ?, 0, 0)
            """, (new_order_id, product_code, quantity_pcs, quantity_sets))
            
            # Prepare data for Excel
            products_for_excel.append({
                'productCode': product_code,
                'quantityPcs': quantity_pcs,
                'quantitySets': quantity_sets,
                'quantity': quantity_pcs + (quantity_sets * 2)  # For backward compatibility
            })

        # Commit the transaction
        conn.commit()
        logger.info(f"Successfully created order {new_order_id} for client {client_id} with {len(products_for_excel)} products")

        # Generate Excel file
        try:
            generate_po_helper(client_id, products_for_excel, new_order_id)
            logger.info(f"Successfully generated Excel file for order {new_order_id}")
        except Exception as excel_error:
            logger.error(f"Error generating Excel file: {str(excel_error)}")
            # Continue even if Excel generation fails

        return jsonify({
            "status": "success",
            "message": "Order created successfully",
            "orderId": new_order_id
        })

    except sqlite3.Error as db_error:
        conn.rollback()
        logger.error(f"Database error in create_order: {str(db_error)}")
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(db_error)}"
        }), 500

    except Exception as e:
        conn.rollback()
        logger.error(f"Unexpected error in create_order: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Unexpected error: {str(e)}"
        }), 500

    finally:
        conn.close()

#==============================================================================
#                         PACKING DEPARTMENT ROUTES
#==============================================================================
# These routes handle the packing process, carton management, and order completion

@app.route('/api/packing_state/<int:order_id>', methods=['GET'])
def get_packing_state(order_id):
    """Hearken, ye seekers of packing knowledge! This mystical endpoint doth reveal
    the current carton number for a specific order's packing journey.

    Like a wise sage consulting ancient scrolls, this function queries the sacred
    order_packing_state table to discover which carton number is currently being filled
    for the specified order. If no record exists in the annals of history, it shall
    return the default value of 1, signifying the beginning of a packing quest.

    Args:
        order_id (int): The ID of the order whose packing state we seek to divine

    Returns:
        JSON: The order ID and current carton number, wrapped in the mystical format of JSON
    """
    logger.info(f"API request received: get_packing_state for order {order_id}")

    conn = get_db_connection()
    c = conn.cursor()
    c.execute("SELECT current_carton_number FROM order_packing_state WHERE order_id = ?", (order_id,))
    result = c.fetchone()
    conn.close()

    if result:
        logger.info(f"Found packing state for order {order_id}: carton {result[0]}")
        return jsonify({"order_id": order_id, "current_carton_number": result[0]})
    else:
        logger.info(f"No packing state found for order {order_id}, returning default carton 1")
        return jsonify({"order_id": order_id, "current_carton_number": 1})

@app.route('/api/packing_state/<int:order_id>', methods=['POST'])
def update_packing_state(order_id):
    """Hearken, ye masters of packing! This mystical endpoint doth allow thee
    to update the current carton number for an order's packing journey.

    Like a scribe recording important events in the kingdom's history, this function
    updates or creates a record in the sacred order_packing_state table to track
    which carton is currently being filled for the specified order.

    Args:
        order_id (int): The ID of the order whose packing state we seek to update

    Request Body:
        JSON: {"current_carton_number": int} - The new carton number to set

    Returns:
        JSON: The order ID and updated carton number, wrapped in the mystical format of JSON
    """
    logger.info(f"API request received: update_packing_state for order {order_id}")

    # Get the new carton number from the request body
    data = request.get_json()
    if not data or 'current_carton_number' not in data:
        logger.error("Missing current_carton_number in request body")
        return jsonify({"error": "Missing current_carton_number"}), 400

    current_carton_number = data['current_carton_number']

    # Update or insert the packing state
    conn = get_db_connection()
    c = conn.cursor()

    # Check if a record already exists for this order
    c.execute("SELECT id FROM order_packing_state WHERE order_id = ?", (order_id,))
    existing = c.fetchone()

    if existing:
        # Update existing record
        c.execute("UPDATE order_packing_state SET current_carton_number = ? WHERE order_id = ?",
                 (current_carton_number, order_id))
        logger.info(f"Updated packing state for order {order_id} to carton {current_carton_number}")
    else:
        # Insert new record
        c.execute("INSERT INTO order_packing_state (order_id, current_carton_number) VALUES (?, ?)",
                 (order_id, current_carton_number))
        logger.info(f"Created new packing state for order {order_id} with carton {current_carton_number}")

    conn.commit()
    conn.close()

    return jsonify({"order_id": order_id, "current_carton_number": current_carton_number})

@app.route('/api/carton_details/<int:order_id>', methods=['GET'])
@app.route('/pack_product', methods=['POST'])
def pack_product():
    """Update products in a carton with both PC and SET quantities.

    This endpoint adds or updates products in a carton for a specific order.
    It handles both PC and SET quantities and updates the packed counts accordingly.

    Request body should contain:
    - orderId: The ID of the order
    - cartonNumber: The carton number to add products to
    - productCode: The product code to add to the carton
    - quantityPackedPcs: The quantity of PC units to pack
    - quantityPackedSets: The quantity of SET units to pack

    Returns:
        JSON: Success or error message
    """
    logger.info("API request received: pack_product")
    
    data = request.get_json()
    
    # Check for required parameters
    if not data or 'orderId' not in data or 'cartonNumber' not in data or 'productCode' not in data:
        logger.error("Missing required parameters in pack_product")
        return jsonify({
            'status': 'error',
            'message': 'orderId, cartonNumber, and productCode are required'
        }), 400
    
    # Ensure at least one quantity field is provided
    if ('quantityPackedPcs' not in data or not data['quantityPackedPcs']) and \
       ('quantityPackedSets' not in data or not data['quantityPackedSets']):
        logger.error("No quantity provided for packing")
        return jsonify({
            'status': 'error',
            'message': 'At least one of quantityPackedPcs or quantityPackedSets must be provided'
        }), 400
    
    order_id = int(data['orderId'])
    carton_number = int(data['cartonNumber'])
    product_code = data['productCode']
    quantity_packed_pcs = int(data.get('quantityPackedPcs', 0))
    quantity_packed_sets = int(data.get('quantityPackedSets', 0))
    
    # Handle backward compatibility - if only quantityPacked is provided
    if 'quantityPacked' in data and quantity_packed_pcs == 0 and quantity_packed_sets == 0:
        quantity_packed_pcs = int(data['quantityPacked'])
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # First verify the order exists and the product is in the order
        cursor.execute("""
            SELECT od.quantity_pcs, od.quantity_sets, od.packed_pcs, od.packed_sets 
            FROM order_details od
            WHERE od.order_id = ? AND od.product_code = ?
        """, (order_id, product_code))
        
        product_details = cursor.fetchone()
        if not product_details:
            logger.warning(f"Product {product_code} not found in order {order_id}")
            conn.close()
            return jsonify({
                'status': 'error',
                'message': f"Product {product_code} not found in order {order_id}"
            }), 404
        
        quantity_pcs, quantity_sets, packed_pcs, packed_sets = product_details
        
        # Calculate new packed totals
        new_packed_pcs = packed_pcs + quantity_packed_pcs
        new_packed_sets = packed_sets + quantity_packed_sets
        
        # Check if the requested quantities would exceed the order quantities
        if new_packed_pcs > quantity_pcs:
            logger.warning(f"Attempted to pack {new_packed_pcs} PCS for product {product_code}, but order only has {quantity_pcs}")
            conn.close()
            return jsonify({
                'status': 'error',
                'message': f"Cannot pack {new_packed_pcs} PCS when order only has {quantity_pcs}"
            }), 400
        
        if new_packed_sets > quantity_sets:
            logger.warning(f"Attempted to pack {new_packed_sets} SETS for product {product_code}, but order only has {quantity_sets}")
            conn.close()
            return jsonify({
                'status': 'error',
                'message': f"Cannot pack {new_packed_sets} SETS when order only has {quantity_sets}"
            }), 400
        
        # Check if this product is already in this carton
        cursor.execute("""
            SELECT id, quantity_packed_pcs, quantity_packed_sets
            FROM packed_products
            WHERE order_id = ? AND carton_number = ? AND product_code = ?
        """, (order_id, carton_number, product_code))
        
        existing_pack = cursor.fetchone()
        
        if existing_pack:
            # Update existing record - add to the existing quantities
            cursor.execute("""
                UPDATE packed_products
                SET quantity_packed_pcs = ?, quantity_packed_sets = ?
                WHERE id = ?
            """, (existing_pack[1] + quantity_packed_pcs, 
                 existing_pack[2] + quantity_packed_sets, 
                 existing_pack[0]))
            
            logger.info(f"Updated product {product_code} in carton {carton_number} for order {order_id}")
        else:
            # Insert new record
            cursor.execute("""
                INSERT INTO packed_products
                (order_id, carton_number, product_code, quantity_packed_pcs, quantity_packed_sets)
                VALUES (?, ?, ?, ?, ?)
            """, (order_id, carton_number, product_code, quantity_packed_pcs, quantity_packed_sets))
            
            logger.info(f"Added product {product_code} to carton {carton_number} for order {order_id}")
        
        # Update the order_details table with the new packed totals
        cursor.execute("""
            UPDATE order_details
            SET packed_pcs = ?, packed_sets = ?
            WHERE order_id = ? AND product_code = ?
        """, (new_packed_pcs, new_packed_sets, order_id, product_code))
        
        conn.commit()
        
        return jsonify({
            'status': 'success',
            'message': 'Product packed successfully',
            'orderId': order_id,
            'cartonNumber': carton_number,
            'productCode': product_code,
            'quantityPackedPcs': quantity_packed_pcs,
            'quantityPackedSets': quantity_packed_sets,
            'newTotalPackedPcs': new_packed_pcs,
            'newTotalPackedSets': new_packed_sets
        })
        
    except sqlite3.Error as e:
        conn.rollback()
        logger.error(f"Database error in pack_product: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Database error: {str(e)}"
        }), 500
        
    except Exception as e:
        conn.rollback()
        logger.error(f"Unexpected error in pack_product: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Unexpected error: {str(e)}"
        }), 500
        
    finally:
        conn.close()

@app.route('/order_details/<int:order_id>', methods=['GET'])
def generate_po_helper(client_id, products, order_id):
    """Generate a purchase order Excel file using the provided data.
    
    This is a helper function that formats product data and calls the appropriate 
    functions to generate the Excel file for a purchase order.
    
    Args:
        client_id (str): The client ID for whom the PO is being generated
        products (list): List of product dictionaries with quantity information
        order_id (int): The order ID for the purchase order
        
    Returns:
        str: Path to the generated Excel file
    """
    logger.info(f"Generating PO for client {client_id}, order {order_id}")
    
    try:
        # Format products for Excel generation
        formatted_products = []
        
        for product in products:
            product_code = product.get('productCode')
            
            # Handle different naming conventions
            quantity_pcs = product.get('quantityPcs', product.get('quantity_pcs', 0))
            quantity_sets = product.get('quantitySets', product.get('quantity_sets', 0))
            
            # Calculate total quantity for backward compatibility
            total_quantity = quantity_pcs + (quantity_sets * 2)  # 1 SET = 2 PCS
            
            formatted_products.append({
                'productCode': product_code,
                'quantityToMake': total_quantity,
                'quantityPcs': quantity_pcs,
                'quantitySets': quantity_sets
            })
        
        # Call the existing generate_po_for_pending_order function
        excel_path = generate_po_for_pending_order(order_id, client_id, formatted_products)
        logger.info(f"Successfully generated PO at {excel_path}")
        return excel_path
        
    except Exception as e:
        logger.error(f"Error in generate_po_helper: {str(e)}")
        raise e  # Re-raise the exception to be handled by the caller

def find_order_id(client_id):
    """Retrieve the latest order_id for a client from the database.

    Args:
        client_id (str): The client ID to search for

    Returns:
        int or None: The most recent order ID for the client, or None if not found
    """
    # Use the global DATABASE_PATH variable to ensure we're using the correct database
    conn = get_db_connection()
    c = conn.cursor()
    c.execute("SELECT order_id FROM orders WHERE client_id = ? ORDER BY id DESC LIMIT 1", (client_id,))
    result = c.fetchone()
    conn.close()
    logger.info(f"Looking for order ID for client {client_id}, found: {result[0] if result else None}")
    return result[0] if result else None

@app.route('/generate_po', methods=['POST'])
def generate_po():
    """Generate a purchase order Excel file based on order data.

    This endpoint creates a formatted Excel purchase order using a template.
    It populates the template with client and order information, formats the products
    by category, and saves the file with a unique name.

    Request body should contain:
    - clientId: The client ID
    - products: Array of products with quantities
    - orderId (optional): The order ID (will be retrieved from database if not provided)

    Returns:
        JSON: Success message and order ID
    """
    logger.info("API request received: generate_po")

    # Retrieve the order data from the request payload
    order_data = request.get_json()
    client_id = order_data['clientId']
    products = order_data['products']

    # Process products to ensure they have the right quantity format
    for product in products:
        # Handle both frontend naming conventions
        # First check for snake_case (quantity_pcs/quantity_sets)
        if 'quantity_pcs' in product or 'quantity_sets' in product:
            quantity_pcs = product.get('quantity_pcs', 0)
            quantity_sets = product.get('quantity_sets', 0)
            # Convert to camelCase for internal use
            product['quantityPcs'] = quantity_pcs
            product['quantitySets'] = quantity_sets
            # Calculate total quantity for backward compatibility
            product['quantityToMake'] = quantity_pcs + (quantity_sets * 2)
        # Then check for camelCase (quantityPcs/quantitySets)
        elif 'quantityPcs' in product or 'quantitySets' in product:
            quantity_pcs = product.get('quantityPcs', 0)
            quantity_sets = product.get('quantitySets', 0)
            # Convert to snake_case for internal use
            product['quantity_pcs'] = quantity_pcs
            product['quantity_sets'] = quantity_sets
            # Calculate total quantity for backward compatibility
            product['quantityToMake'] = quantity_pcs + (quantity_sets * 2)
        # If the product only has quantityToMake, that's fine too

    # Get the order ID from the payload or database
    order_id = order_data.get('orderId')

    # If order_id is provided in the payload, verify it exists in the database
    if order_id:
        conn = get_db_connection()
        c = conn.cursor()
        c.execute("SELECT COUNT(*) FROM orders WHERE order_id = ?", (order_id,))
        exists = c.fetchone()[0] > 0
        conn.close()

        if not exists:
            logger.warning(f"Provided order ID {order_id} not found in database")
            # If the provided order ID doesn't exist, try to find another one
            order_id = None

    # If no valid order_id yet, try to find the latest one for this client
    if not order_id:
        order_id = find_order_id(client_id)

    # If we still don't have an order ID, return an error
    if not order_id:
        logger.warning(f"No valid order ID found for client {client_id}")
        return jsonify({'error': 'No valid order ID found for the client. Please create an order first.'}), 400

    logger.info(f"Using order ID {order_id} for PO generation")

    # Retrieve the scheduled date for the order
    conn = get_db_connection()
    c = conn.cursor()
    c.execute("SELECT scheduled_date FROM orders WHERE order_id = ?", (order_id,))
    scheduled_date = c.fetchone()
    conn.close()

    logger.info(f"Retrieved scheduled date for order {order_id}: {scheduled_date}")

    # Format the scheduled date
    if scheduled_date and scheduled_date[0]:
        try:
            # Parse the date (adjust the parsing format if your stored date format is different)
            date_obj = datetime.strptime(scheduled_date[0], '%Y-%m-%d')  # Adjust this format based on how it's stored
            scheduled_date = date_obj.strftime('%d-%m-%Y')
        except ValueError:
            scheduled_date = 'Not Scheduled'
    else:
        scheduled_date = 'Not Scheduled'

    # Get client transport and booking information from client_db.db
    # Use a try-except block to handle potential database issues
    try:
        client_info = None
        party_name_value = ''

        # Use get_all_clients() to fetch all clients and find the matching one
        try:
            from flask import current_app
            with current_app.app_context():
                all_clients_response = get_all_clients()
                if hasattr(all_clients_response, 'get_json'):
                    all_clients_data = all_clients_response.get_json()
                    if all_clients_data and 'data' in all_clients_data:
                        for client in all_clients_data['data']:
                            # Try both 'clientId' and 'ClientId' for robustness
                            if str(client.get('clientId', client.get('ClientId', ''))) == str(client_id):
                                party_name_value = client.get('Party_Name') or client.get('partyName') or ''
                                break
        except Exception as e:
            logger.warning(f"Could not fetch party name from get_all_clients: {str(e)}")

        # First check if the client_db.db file exists
        if os.path.exists('client_db.db'):
            client_conn = sqlite3.connect('client_db.db')
            client_cursor = client_conn.cursor()

            # Check if the clients table exists
            client_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='clients'")
            table_exists = client_cursor.fetchone() is not None

            if table_exists:
                # Try to get client information
                client_cursor.execute("PRAGMA table_info(clients)")
                columns = [column[1] for column in client_cursor.fetchall()]

                # Check if the required columns exist
                if 'client_Id' in columns:
                    # Build a query based on available columns
                    query_columns = []
                    if 'Transport' in columns:
                        query_columns.append('Transport')
                    else:
                        query_columns.append("'' AS Transport")

                    if 'Booking' in columns:
                        query_columns.append('Booking')
                    else:
                        query_columns.append("'' AS Booking")

                    if 'Logo' in columns:
                        query_columns.append('Logo')
                    else:
                        query_columns.append("'' AS Logo")

                    # Execute the query
                    query = f"SELECT {', '.join(query_columns)} FROM clients WHERE client_Id = ?"
                    client_cursor.execute(query, (client_id,))
                    client_info = client_cursor.fetchone()
                    print(client_info)

            client_conn.close()

        # Extract transport and booking, handle None/empty values (prefer values from get_all_clients if available)
        if not transport_value:
            transport_value = client_info[0] if client_info and len(client_info) > 0 and client_info[0] else ''
        if not booking_value:
            booking_value = client_info[1] if client_info and len(client_info) > 1 and client_info[1] else ''
        if not logo_value:
            logo_value = client_info[2] if client_info and len(client_info) > 2 and client_info[2] else ''

        # Set the merged cell values as requested
        transport = f"Transport : {transport_value}"
        booking = f"Booking : {booking_value}"
        marco = f"Marko : {logo_value}"

        logger.info(f"Retrieved client info for {client_id}: Transport={transport}, Booking={booking}, Marko={marco}, PartyName={party_name_value}")

    except Exception as e:
        # Log the error but continue with default values
        logger.error(f"Error retrieving client information: {str(e)}")
        transport = "Transport : "
        booking = "Booking : "
        marco = "Marko : "
        party_name_value = ''

    # Load the Excel template with the new path
    # Try multiple possible template paths
    template_paths = [
        'D:/COMMON DATA NEW/Handle ERP/Order PO/PO TEMPLATE.xlsx',  # New path
        'D:/ORDERS/PO TEMPLATE.xlsx',                               # Old path  
        'PO TEMPLATE.xlsx'                                          # Local path
    ]

    template_path = None
    for path in template_paths:
        if os.path.exists(path):
            template_path = path
            logger.info(f"Found PO template at: {path}")
            break

    if not template_path:
        logger.error("PO template not found in any of the expected locations")
        return jsonify({'error': 'PO template file not found. Please check the template path.'}), 500

    try:
        workbook = load_workbook(template_path)

        # Check if the PO worksheet exists
        if 'Sheet1' not in workbook.sheetnames:
            logger.error(f"'PO' worksheet not found in template. Available sheets: {workbook.sheetnames}")
            return jsonify({'error': "'PO' worksheet not found in template"}), 500

        worksheet = workbook['Sheet1']
    except Exception as e:
        logger.error(f"Error loading Excel template: {str(e)}")
        return jsonify({'error': f"Error loading Excel template: {str(e)}"}), 500

    # Update the Excel file with all the data according to the new template layout
    # A1: Transport
    worksheet['A1'] = transport
    # D1: Booking
    worksheet['D1'] = booking
    # H1: Order ID
    worksheet['H1'] = f'Order ID - {order_id}'
    # A2: Marko
    worksheet['A2'] = marco
    # C2: Client ID
    worksheet['C2'] = f'Client ID - {client_id}'
    # H2: Order Date
    worksheet['H2'] = f'Order Date - {datetime.now().strftime("%d.%m.%y")}'
    # A3: Party Name
    worksheet['A3'] = f'Party Name : {party_name_value}'
    # H3: Scheduled Date
    worksheet['H3'] = f'Disp. Date - {scheduled_date}'

    # Calculate total quantity considering both PCS and SETS
    total_quantity = 0
    for product in products:
        if 'quantityPcs' in product or 'quantitySets' in product:
            # New format with separate PCS and SETS quantities
            quantity_pcs = product.get('quantityPcs', 0)
            quantity_sets = product.get('quantitySets', 0)
            # Calculate total (1 SET = 2 PCS)
            total_quantity += quantity_pcs + (quantity_sets * 2)
        else:
            # Old format with single quantity field
            total_quantity += product.get('quantityToMake', 0)

    # Set the total in cell I4 as per the new template
    worksheet['I4'] = total_quantity

    # Categorize and format the products
    categorize_and_format_products(products, worksheet)

    # Save the file with unique naming
    # Try multiple possible output directories
    possible_dirs = [
        'D:/COMMON DATA NEW/Handle ERP/Order PO',  # New path
        'D:/ORDERS',                               # Old path
        'Orders'                                   # Local path
    ]

    # Find the first directory that exists or can be created
    orders_dir = None
    for dir_path in possible_dirs:
        try:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)

            # Test if we can write to this directory
            test_file = os.path.join(dir_path, '.test_write')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)

            orders_dir = dir_path
            logger.info(f"Using output directory: {dir_path}")
            break
        except Exception as e:
            logger.warning(f"Cannot use directory {dir_path}: {str(e)}")

    # If no suitable directory found, use the current directory
    if not orders_dir:
        orders_dir = '.'
        logger.warning("Using current directory for output as no other directory is accessible")

    base_file_name = f'{client_id}_{datetime.now().strftime("%d.%m.%y")}.xlsx'
    file_path = os.path.join(orders_dir, base_file_name)

    # Check if file exists and create a unique filename
    counter = 1
    while os.path.exists(file_path):
        file_name = f'{client_id}_{datetime.now().strftime("%d.%m.%y")}({counter}).xlsx'
        file_path = os.path.join(orders_dir, file_name)
        counter += 1

    # Save the workbook
    workbook.save(file_path)
    logger.info(f"Purchase Order generated successfully: {file_path}")

    # Return a success response
    return jsonify({
        'message': 'Purchase Order generated successfully',
        'order_id': order_id
    }), 200


def categorize_and_format_products(products, worksheet):
    """Categorize and format products in the Excel worksheet.

    This function organizes products by category and formats them in the worksheet
    in a two-column layout. It handles product grouping, quantity aggregation,
    and proper spacing between categories.

    The new template layout has:
    - Column A: Product photos (AB version)
    - Column B: Product names
    - Column C: Quantities in PCS
    - Column D: Quantities in SETS
    - Column F: Product photos (right side)
    - Column G: Product codes
    - Column H: Quantities in PCS (right side)
    - Column I: Quantities in SETS (right side)

    Products are categorized by product name (e.g., all 110 or 143 together).

    Args:
        products (list): List of product dictionaries with productCode and quantities
        worksheet (openpyxl.worksheet): The Excel worksheet to update
    """
    # Import necessary modules for image handling and file operations
    from openpyxl.drawing.image import Image as XLImage
    import os
    import logging
    from PIL import Image
    import io

    # Setup logging
    logger = logging.getLogger(__name__)

    # Define image dimensions and cell adjustments
    IMAGE_WIDTH = 120  # pixels
    IMAGE_HEIGHT = 120  # pixels
    ROW_HEIGHT = 15    # points
    COL_WIDTH = 16     # characters

    # Set column widths for better layout
    worksheet.column_dimensions['A'].width = COL_WIDTH
    worksheet.column_dimensions['F'].width = COL_WIDTH

    # Group products by their base product code (before the underscore)
    product_groups = {}

    for product in products:
        product_code = product['productCode']

        # Extract the base product code (before the underscore if present)
        base_code = product_code.split('_')[0] if '_' in product_code else product_code

        # Handle both naming conventions (camelCase and snake_case)
        if 'quantity_pcs' in product or 'quantity_sets' in product:
            # Snake case format
            quantity_pcs = product.get('quantity_pcs', 0)
            quantity_sets = product.get('quantity_sets', 0)
        elif 'quantityPcs' in product or 'quantitySets' in product:
            # Camel case format
            quantity_pcs = product.get('quantityPcs', 0)
            quantity_sets = product.get('quantitySets', 0)
        else:
            # Old format with single quantity field - assume all are PCS
            quantity_pcs = product.get('quantityToMake', 0)
            quantity_sets = 0

        if base_code not in product_groups:
            product_groups[base_code] = []

        product_groups[base_code].append({
            'product_code': product_code,
            'quantity_pcs': quantity_pcs,
            'quantity_sets': quantity_sets
        })

    # Sort the product groups by base code for consistent ordering
    sorted_groups = sorted(product_groups.items())

    # Split the groups into left and right sides
    mid_point = len(sorted_groups) // 2
    left_groups = sorted_groups[:mid_point]
    right_groups = sorted_groups[mid_point:]

    # Function to find and prepare product image
    def find_product_image(base_code):
        """Find and prepare a product image for the given base code.

        Args:
            base_code (str): The base product code to find an image for

        Returns:
            tuple: (image_object, image_path) if found, (None, None) if not found
        """
        # Create a list of possible image paths and formats
        possible_paths = [
            f"static/product_images/{base_code}_ab.jpg",
            f"static/product_images/{base_code}_ab.png",
            f"static/product_images/{base_code}_ab.jpeg",
            f"static/product_images/{base_code}_ab.gif",
            f"static/product_images/{base_code.upper()}_ab.jpg",
            f"static/product_images/{base_code.upper()}_ab.png",
            f"static/product_images/{base_code}.jpg",
            f"static/product_images/{base_code}.png",
            f"product_images/{base_code}_ab.jpg",
            f"product_images/{base_code}_ab.png",
            f"static\\product_images\\{base_code}_ab.jpg",
            f"static\\product_images\\{base_code}_ab.png"
        ]

        logger.info(f"Searching for image for product {base_code}")

        # Check if app object exists and has static_folder attribute
        app_static_paths = []
        if 'app' in globals() and hasattr(app, 'static_folder') and app.static_folder:
            static_product_images = os.path.join(app.static_folder, 'product_images')
            if os.path.exists(static_product_images):
                app_static_paths = [
                    os.path.join(static_product_images, f"{base_code}_ab.jpg"),
                    os.path.join(static_product_images, f"{base_code}_ab.png"),
                    os.path.join(static_product_images, f"{base_code}.jpg"),
                    os.path.join(static_product_images, f"{base_code}.png")
                ]

                # Check for subdirectory for this product
                product_subdir = os.path.join(static_product_images, base_code)
                if os.path.exists(product_subdir) and os.path.isdir(product_subdir):
                    app_static_paths.extend([
                        os.path.join(product_subdir, f"{base_code}_ab.jpg"),
                        os.path.join(product_subdir, f"{base_code}_ab.png"),
                        os.path.join(product_subdir, "ab.jpg"),
                        os.path.join(product_subdir, "ab.png")
                    ])

        # Combine all possible paths
        all_paths = possible_paths + app_static_paths

        # Try each path
        for image_path in all_paths:
            if os.path.exists(image_path):
                logger.info(f"Found image at {image_path}")
                try:
                    # Use PIL to properly resize the image maintaining aspect ratio
                    with Image.open(image_path) as img_pil:
                        # Calculate new dimensions while maintaining aspect ratio
                        aspect_ratio = img_pil.width / img_pil.height

                        if aspect_ratio > 1:  # Wider than tall
                            new_width = IMAGE_WIDTH
                            new_height = int(IMAGE_WIDTH / aspect_ratio)
                        else:  # Taller than wide or square
                            new_height = IMAGE_HEIGHT
                            new_width = int(IMAGE_HEIGHT * aspect_ratio)

                        # Resize image with antialiasing
                        resized_img = img_pil.resize((new_width, new_height), Image.LANCZOS)

                        # Create a blank white background of the target size
                        background = Image.new('RGBA', (IMAGE_WIDTH, IMAGE_HEIGHT), (255, 255, 255, 0))

                        # Calculate position to center the image
                        x_offset = (IMAGE_WIDTH - new_width) // 2
                        y_offset = (IMAGE_HEIGHT - new_height) // 2

                        # Paste the resized image onto the background
                        background.paste(resized_img, (x_offset, y_offset))

                        # Save to BytesIO to create XLImage
                        buffer = io.BytesIO()
                        background.save(buffer, format='PNG')
                        buffer.seek(0)

                        # Create openpyxl image
                        xl_img = XLImage(buffer)
                        xl_img.width = IMAGE_WIDTH
                        xl_img.height = IMAGE_HEIGHT

                        return xl_img, image_path
                except Exception as e:
                    logger.error(f"Error processing image {image_path}: {str(e)}")
                    continue

        # If no image found in specific paths, try directory search
        if 'app' in globals() and hasattr(app, 'static_folder') and app.static_folder:
            static_product_images = os.path.join(app.static_folder, 'product_images')
            if os.path.exists(static_product_images):
                logger.info(f"Searching entire product_images directory for {base_code}")
                for root, _, files in os.walk(static_product_images):
                    for file in files:
                        if (base_code.lower() in file.lower() and
                            (file.lower().endswith('.jpg') or file.lower().endswith('.png'))):
                            image_path = os.path.join(root, file)
                            try:
                                logger.info(f"Found potential match: {image_path}")
                                with Image.open(image_path) as img_pil:
                                    # Resize using the same logic as above
                                    aspect_ratio = img_pil.width / img_pil.height

                                    if aspect_ratio > 1:
                                        new_width = IMAGE_WIDTH
                                        new_height = int(IMAGE_WIDTH / aspect_ratio)
                                    else:
                                        new_height = IMAGE_HEIGHT
                                        new_width = int(IMAGE_HEIGHT * aspect_ratio)

                                    resized_img = img_pil.resize((new_width, new_height), Image.LANCZOS)

                                    background = Image.new('RGBA', (IMAGE_WIDTH, IMAGE_HEIGHT), (255, 255, 255, 0))
                                    x_offset = (IMAGE_WIDTH - new_width) // 2
                                    y_offset = (IMAGE_HEIGHT - new_height) // 2
                                    background.paste(resized_img, (x_offset, y_offset))

                                    buffer = io.BytesIO()
                                    background.save(buffer, format='PNG')
                                    buffer.seek(0)

                                    xl_img = XLImage(buffer)
                                    xl_img.width = IMAGE_WIDTH
                                    xl_img.height = IMAGE_HEIGHT

                                    return xl_img, image_path
                            except Exception as img_error:
                                logger.warning(f"Error loading image {image_path}: {str(img_error)}")

        logger.warning(f"No image found for product {base_code}")
        return None, None

    # Function to add product image to worksheet with name above it
    def add_product_image(worksheet, base_code, start_row, end_row, col):
        """Add a product image to the worksheet in a merged cell area with product name above it.

        Args:
            worksheet (openpyxl.worksheet): The worksheet to add the image to
            base_code (str): The base product code for the image
            start_row (int): The starting row for the merged cell area
            end_row (int): The ending row for the merged cell area
            col (int): The column to place the image

        Returns:
            bool: True if image was added successfully, False otherwise
        """
        try:
            from openpyxl.styles import Alignment, Border, Side, Font, PatternFill

            # Get the product name
            product_name = get_product_name(base_code)

            # Ensure we have at least one row for the image
            if end_row < start_row:
                end_row = start_row

            # Set minimum height for the image area
            min_height = ROW_HEIGHT

            # Add product name in the row above the image
            name_row = start_row - 1
            name_cell = worksheet.cell(row=name_row, column=col)
            name_cell.value = product_name

            # Style the product name cell
            thick_border = Side(border_style="medium", color="000000")
            thin_border = Side(border_style="thin", color="000000")
            name_cell.border = Border(
                top=thick_border,
                left=thick_border,
                right=thick_border,
                bottom=thick_border
            )
            name_cell.font = Font(bold=True, size=11)
            name_cell.alignment = Alignment(horizontal='center', vertical='center')
            name_cell.fill = PatternFill(start_color="E0E0E0", end_color="E0E0E0", fill_type="solid")

            # Set height for the name row
            worksheet.row_dimensions[name_row].height = 20

            # If we have multiple rows, merge them for the image
            if end_row > start_row:
                # Merge cells for the image
                merge_range = f"{chr(64+col)}{start_row}:{chr(64+col)}{end_row}"
                worksheet.merge_cells(merge_range)

                # Calculate total height needed based on number of rows
                rows_count = end_row - start_row + 1
                total_height = min_height * rows_count

                # Distribute height evenly
                row_height = total_height / rows_count
                for r in range(start_row, end_row + 1):
                    worksheet.row_dimensions[r].height = row_height
            else:
                # Just set the height for a single row
                worksheet.row_dimensions[start_row].height = min_height * 1.5  # Make single images a bit larger

            # Find and prepare the image
            img, _ = find_product_image(base_code)  # Using _ to ignore the image_path

            if img:
                # Calculate the cell reference for the top-left cell
                cell_ref = f"{chr(64+col)}{start_row}"

                # Add image to worksheet with proper alignment
                img.anchor = cell_ref
                worksheet.add_image(img)

                # Add product code as text in cell for accessibility and fallback
                cell = worksheet.cell(row=start_row, column=col)
                cell.value = base_code

                # Apply styling to the image cell
                cell.border = Border(top=thin_border, left=thin_border, right=thin_border, bottom=thin_border)
                cell.alignment = Alignment(horizontal='center', vertical='center')

                return True
            else:
                # If no image found, just add a text placeholder
                cell = worksheet.cell(row=start_row, column=col)
                cell.value = f"{base_code}_ab"
                return False

        except Exception as e:
            logger.error(f"Error adding image for {base_code}: {str(e)}")
            worksheet.cell(row=start_row, column=col, value=f"{base_code}_ab")
            return False

    # Prepare worksheet header rows
    def setup_worksheet_headers(worksheet):
        """Set up the worksheet headers and formatting.

        Args:
            worksheet (openpyxl.worksheet): The worksheet to set up
        """
        from openpyxl.styles import Border, Side, PatternFill, Alignment, Font

        # Define borders and styles
        thin_border = Side(border_style="thin", color="000000")
        border = Border(top=thin_border, left=thin_border, right=thin_border, bottom=thin_border)
        header_fill = PatternFill(start_color="D9D9D9", end_color="D9D9D9", fill_type="solid")
        header_font = Font(bold=True, size=11)

        # Set column headers
        headers = {
            'A': 'Product Photo',
            'B': 'Product Name',
            'C': 'Quantity (PCS)',
            'D': 'Quantity (SETS)',
            'F': 'Product Photo',
            'G': 'Product Name',
            'H': 'Quantity (PCS)',
            'I': 'Quantity (SETS)'
        }

        # Apply headers and formatting
        for col, header in headers.items():
            cell = worksheet[f'{col}5']
            cell.value = header
            cell.border = border
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = Alignment(horizontal='center', vertical='center')

        # Set column widths
        worksheet.column_dimensions['A'].width = 20
        worksheet.column_dimensions['B'].width = 30
        worksheet.column_dimensions['C'].width = 15
        worksheet.column_dimensions['D'].width = 15
        worksheet.column_dimensions['F'].width = 20
        worksheet.column_dimensions['G'].width = 30
        worksheet.column_dimensions['H'].width = 15
        worksheet.column_dimensions['I'].width = 15

        logger.info("Headers set up successfully")

    # Set up the worksheet headers
    setup_worksheet_headers(worksheet)

    # Process the left side (columns A-D)
    current_row = 7  # Start at row 7 to leave room for product name above image
    for base_code, products_in_group in left_groups:
        # Track group start row for the image and products
        group_start_row = current_row

        # Add each product in the group
        for product in products_in_group:
            # Column B: Product name
            worksheet.cell(row=current_row, column=2, value=product['product_code'])
            # Column C: Quantity in PCS
            worksheet.cell(row=current_row, column=3, value=product['quantity_pcs'])
            # Column D: Quantity in SETS
            worksheet.cell(row=current_row, column=4, value=product['quantity_sets'])
            current_row += 1

        # Pad with empty rows if less than 5 rows used for this group
        rows_used = current_row - group_start_row
        if rows_used < 5:
            for _ in range(5 - rows_used):
                # Leave columns B, C, D empty
                current_row += 1

        # Calculate the end row for the group (current_row - 1 is the last row we added)
        group_end_row = current_row - 1

        # Add product image in column A, merging cells for the entire group
        # The product name will be added in the row above the image (group_start_row - 1)
        add_product_image(worksheet, base_code, group_start_row, group_end_row, 1)

        # Add a blank row after each group for separation
        current_row += 1

    # Process the right side (columns F-I)
    current_row = 7  # Start at row 7 to leave room for product name above image
    for base_code, products_in_group in right_groups:
        # Track group start row for the image and products
        group_start_row = current_row

        # Add each product in the group
        for product in products_in_group:
            # Column G: Product code
            worksheet.cell(row=current_row, column=7, value=product['product_code'])
            # Column H: Quantity in PCS
            worksheet.cell(row=current_row, column=8, value=product['quantity_pcs'])
            # Column I: Quantity in SETS
            worksheet.cell(row=current_row, column=9, value=product['quantity_sets'])
            current_row += 1

        # Pad with empty rows if less than 5 rows used for this group
        rows_used = current_row - group_start_row
        if rows_used < 5:
            for _ in range(5 - rows_used):
                # Leave columns G, H, I empty
                current_row += 1

        # Calculate the end row for the group (current_row - 1 is the last row we added)
        group_end_row = current_row - 1

        # Add product image in column F, merging cells for the entire group
        # The product name will be added in the row above the image (group_start_row - 1)
        add_product_image(worksheet, base_code, group_start_row, group_end_row, 6)

        # Add a blank row after each group for separation
        current_row += 1

    # Apply final formatting
    def apply_final_formatting(worksheet):
        """Apply final formatting to the worksheet for better appearance.

        Args:
            worksheet (openpyxl.worksheet): The worksheet to format
        """
        from openpyxl.styles import Border, Side, PatternFill, Alignment

        # Define borders
        thin_border = Side(border_style="thin", color="000000")
        border = Border(top=thin_border, left=thin_border, right=thin_border, bottom=thin_border)

        # Define fills
        header_fill = PatternFill(start_color="D9D9D9", end_color="D9D9D9", fill_type="solid")

        # Format headers
        for row in [1, 3, 5]:
            for col in range(1, 10):
                cell = worksheet.cell(row=row, column=col)
                if cell.value:  # Only format cells that have values
                    cell.border = border
                    if row == 5:  # Column headers
                        cell.fill = header_fill
                    cell.alignment = Alignment(horizontal='center', vertical='center')

        # Format data cells
        max_row = worksheet.max_row
        for row in range(6, max_row + 1):
            for col in [2, 3, 4, 7, 8, 9]:  # Skip image columns
                cell = worksheet.cell(row=row, column=col)
                if cell.value:  # Only format cells that have values
                    cell.border = border
                    if col in [3, 4, 8, 9]:  # Quantity columns
                        cell.alignment = Alignment(horizontal='center')

    # Apply final formatting
    apply_final_formatting(worksheet)

    # Remove any unused rows
    last_used_row = get_last_used_row(worksheet)
    if last_used_row > 0 and last_used_row < worksheet.max_row:
        worksheet.delete_rows(last_used_row + 1, worksheet.max_row - last_used_row)

def get_last_used_row(worksheet):
    """Find the last used row in a worksheet.

    Args:
        worksheet (openpyxl.worksheet): The Excel worksheet to check

    Returns:
        int: The row number of the last used row
    """
    for row in range(worksheet.max_row, 0, -1):
        if any(worksheet.cell(row=row, column=col).value for col in range(1, worksheet.max_column + 1)):
            return row
    return 0

def categorize_product(product_code, category_order, khuti_categories):
    """Categorize a product based on its product code.

    This function determines the category of a product based on its code
    using a set of predefined rules and patterns.

    Args:
        product_code (str): The product code to categorize
        category_order (list): List of main categories in order
        khuti_categories (list): List of khuti categories

    Returns:
        str: The category of the product
    """
    # Check for the "corner" category based on the patterns *10, *12, *14, *16, *18, *8
    for pattern in ['X10', 'X12', 'X14', 'X16', 'X18', 'X8']:
        if pattern in product_code:
            return 'corner'

    # Check main categories
    for category in category_order:
        if product_code.endswith(category):
            return category

    # Check Khuti categories
    for category in khuti_categories:
        if category.replace('*', '') in product_code:
            return category

    # Check for *6 category
    if '*' in product_code and product_code[-1] == '6':
        return '*6'

    # For products categorized as "Other", use the last two letters of the product code
    return f'Other'

#PDF DIRECTORY
PDF_DIRECTORY = os.path.join(os.path.dirname(__file__), 'static', 'pdfs')
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.colors import HexColor
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib.units import inch
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from collections import defaultdict
from io import BytesIO
import sqlite3
import os

def add_page_number(canvas, doc):
    page_number = canvas.getPageNumber()
    text = f"Page {page_number}"
    canvas.saveState()
    canvas.drawString(500, 10, text)  # Position the page number
    canvas.restoreState()

def register_fonts():
    pdfmetrics.registerFont(TTFont('Montserrat-Bold', 'fonts/Montserrat-Bold.ttf'))
    pdfmetrics.registerFont(TTFont('Montserrat-Regular', 'fonts/Montserrat-Regular.ttf'))

primary_color = colors.HexColor("#175935")
secondary_color = colors.HexColor("#6C757D")
text_color = colors.HexColor("#212529")
highlight_color = colors.lightcoral
gold_accent = colors.HexColor("#C9B037")  # Subtle, elegant gold
dark_green = colors.HexColor("#2F4F2F")  # Dark matte green for headers

def create_executive_summary(order_items, styles):
    """Create a clean executive summary section with only requested details"""
    total_order_pcs = sum(item[1] or 0 for item in order_items)
    total_order_sets = sum(item[2] or 0 for item in order_items)
    
    # Calculate packed totals
    total_packed_pcs = sum(item[3] if len(item) > 3 else 0 for item in order_items)
    total_packed_sets = sum(item[4] if len(item) > 4 else 0 for item in order_items)
    
    # Create clean summary cards
    summary_style = ParagraphStyle(
        'SummaryLabel',
        parent=styles['Normal'],
        fontName='Montserrat-Regular',
        fontSize=11,
        textColor=colors.HexColor("#555555"),
        alignment=1
    )
    
    value_style = ParagraphStyle(
        'SummaryValue',
        parent=styles['Normal'],
        fontName='Montserrat-Bold',
        fontSize=16,
        textColor=colors.HexColor("#2c3e50"),
        alignment=1
    )
    
    summary_data = [
        [
            Paragraph("ORDER PCS", summary_style),
            Paragraph("PACKED PCS", summary_style),
            Paragraph("ORDER SETS", summary_style),
            Paragraph("PACKED SETS", summary_style)
        ],
        [
            Paragraph(str(total_order_pcs), value_style),
            Paragraph(str(total_packed_pcs), value_style),
            Paragraph(str(total_order_sets), value_style),
            Paragraph(str(total_packed_sets), value_style)
        ]
    ]
    
    summary_table = Table(summary_data, colWidths=[1.75*inch, 1.75*inch, 1.75*inch, 1.75*inch])
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor("#f8f9fa")),
        ('BACKGROUND', (0, 1), (-1, 1), colors.white),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('BOX', (0, 0), (-1, -1), 1, colors.HexColor("#dee2e6")),
        ('LINEBELOW', (0, 0), (-1, 0), 1, colors.HexColor("#dee2e6")),
    ]))
    
    return summary_table

def create_pcs_order_table(order_items, styles):
    """Create ORDER PCS details table"""
    
    # Professional header style with dark green background
    header_style = ParagraphStyle(
        'TableHeader',
        parent=styles['Normal'],
        fontName='Montserrat-Bold',
        fontSize=10,
        textColor=colors.white,
        alignment=1
    )
    
    # Clean data style
    data_style = ParagraphStyle(
        'TableData',
        parent=styles['Normal'],
        fontName='Montserrat-Regular',
        fontSize=9,
        textColor=colors.HexColor("#2c3e50"),
        alignment=1
    )
    
    # Create PCS table
    table_data = [[
        Paragraph('Product Code', header_style),
        Paragraph('Order PCS', header_style),
        Paragraph('Packed PCS', header_style),
        Paragraph('Pending PCS', header_style),
        Paragraph('Status', header_style)
    ]]
    
    total_order_pcs = total_packed_pcs = 0
    
    for item in order_items:
        product_code = item[0]
        quantity_pcs = item[1] or 0
        packed_pcs = item[3] if len(item) > 3 else 0
        pending_pcs = quantity_pcs - packed_pcs
        
        # Determine status for PCS
        if pending_pcs <= 0:
            status = "DONE"
            status_color = colors.HexColor("#27ae60")
        else:
            status = "PENDING"
            status_color = colors.HexColor("#e74c3c")
        
        status_style = ParagraphStyle('Status', parent=data_style, textColor=status_color, fontName='Montserrat-Bold')
        
        table_data.append([
            Paragraph(str(product_code), data_style),
            Paragraph(str(quantity_pcs), data_style),
            Paragraph(str(packed_pcs), data_style),
            Paragraph(str(pending_pcs), data_style),
            Paragraph(status, status_style)
        ])
        
        total_order_pcs += quantity_pcs
        total_packed_pcs += packed_pcs
    
    # Add totals row with dark green background
    total_style = ParagraphStyle('Total', parent=header_style, fontSize=10)
    table_data.append([
        Paragraph('TOTAL', total_style),
        Paragraph(str(total_order_pcs), total_style),
        Paragraph(str(total_packed_pcs), total_style),
        Paragraph(str(total_order_pcs - total_packed_pcs), total_style),
        Paragraph('', total_style)
    ])
    
    # Create table with optimized column widths for PCS
    pcs_table = Table(table_data, colWidths=[2*inch, 1.3*inch, 1.3*inch, 1.3*inch, 1.1*inch])
    
    # Apply clean, professional styling with dark green header
    pcs_table.setStyle(TableStyle([
        # Header styling with dark green background
        ('BACKGROUND', (0, 0), (-1, 0), dark_green),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
        ('FONTNAME', (0, 0), (-1, 0), 'Montserrat-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        
        # Data rows styling
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('FONTNAME', (0, 1), (-1, -2), 'Montserrat-Regular'),
        ('FONTSIZE', (0, 1), (-1, -2), 9),
        
        # Alternating row colors
        ('ROWBACKGROUNDS', (0, 1), (-1, -2), [colors.white, colors.HexColor("#f8f9fa")]),
        
        # Total row styling with dark green background
        ('BACKGROUND', (0, -1), (-1, -1), dark_green),
        ('TEXTCOLOR', (0, -1), (-1, -1), colors.white),
        ('FONTNAME', (0, -1), (-1, -1), 'Montserrat-Bold'),
        
        # Borders and padding
        ('BOX', (0, 0), (-1, -1), 1, colors.HexColor("#bdc3c7")),
        ('INNERGRID', (0, 0), (-1, -1), 0.5, colors.HexColor("#ecf0f1")),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('TOPPADDING', (0, 0), (-1, -1), 6),
    ]))
    
    return pcs_table

def create_sets_order_table(order_items, styles):
    """Create ORDER SETS details table"""
    
    # Professional header style with dark green background
    header_style = ParagraphStyle(
        'TableHeader',
        parent=styles['Normal'],
        fontName='Montserrat-Bold',
        fontSize=10,
        textColor=colors.white,
        alignment=1
    )
    
    # Clean data style
    data_style = ParagraphStyle(
        'TableData',
        parent=styles['Normal'],
        fontName='Montserrat-Regular',
        fontSize=9,
        textColor=colors.HexColor("#2c3e50"),
        alignment=1
    )
    
    # Create SETS table
    table_data = [[
        Paragraph('Product Code', header_style),
        Paragraph('Order SETS', header_style),
        Paragraph('Packed SETS', header_style),
        Paragraph('Pending SETS', header_style),
        Paragraph('Status', header_style)
    ]]
    
    total_order_sets = total_packed_sets = 0
    
    for item in order_items:
        product_code = item[0]
        quantity_sets = item[2] or 0
        packed_sets = item[4] if len(item) > 4 else 0
        pending_sets = quantity_sets - packed_sets
        
        # Determine status for SETS
        if pending_sets <= 0:
            status = "DONE"
            status_color = colors.HexColor("#27ae60")
        else:
            status = "PENDING"
            status_color = colors.HexColor("#e74c3c")
        
        status_style = ParagraphStyle('Status', parent=data_style, textColor=status_color, fontName='Montserrat-Bold')
        
        table_data.append([
            Paragraph(str(product_code), data_style),
            Paragraph(str(quantity_sets), data_style),
            Paragraph(str(packed_sets), data_style),
            Paragraph(str(pending_sets), data_style),
            Paragraph(status, status_style)
        ])
        
        total_order_sets += quantity_sets
        total_packed_sets += packed_sets
    
    # Add totals row with dark green background
    total_style = ParagraphStyle('Total', parent=header_style, fontSize=10)
    table_data.append([
        Paragraph('TOTAL', total_style),
        Paragraph(str(total_order_sets), total_style),
        Paragraph(str(total_packed_sets), total_style),
        Paragraph(str(total_order_sets - total_packed_sets), total_style),
        Paragraph('', total_style)
    ])
    
    # Create table with optimized column widths for SETS
    sets_table = Table(table_data, colWidths=[2*inch, 1.3*inch, 1.3*inch, 1.3*inch, 1.1*inch])
    
    # Apply clean, professional styling with dark green header
    sets_table.setStyle(TableStyle([
        # Header styling with dark green background
        ('BACKGROUND', (0, 0), (-1, 0), dark_green),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
        ('FONTNAME', (0, 0), (-1, 0), 'Montserrat-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        
        # Data rows styling
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('FONTNAME', (0, 1), (-1, -2), 'Montserrat-Regular'),
        ('FONTSIZE', (0, 1), (-1, -2), 9),
        
        # Alternating row colors
        ('ROWBACKGROUNDS', (0, 1), (-1, -2), [colors.white, colors.HexColor("#f8f9fa")]),
        
        # Total row styling with dark green background
        ('BACKGROUND', (0, -1), (-1, -1), dark_green),
        ('TEXTCOLOR', (0, -1), (-1, -1), colors.white),
        ('FONTNAME', (0, -1), (-1, -1), 'Montserrat-Bold'),
        
        # Borders and padding
        ('BOX', (0, 0), (-1, -1), 1, colors.HexColor("#bdc3c7")),
        ('INNERGRID', (0, 0), (-1, -1), 0.5, colors.HexColor("#ecf0f1")),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('TOPPADDING', (0, 0), (-1, -1), 6),
    ]))
    
    return sets_table

def generate_combined_order_pdf(order_id, invoice_number):
    register_fonts()

    conn = sqlite3.connect('Trione_ERP_Database.db')
    conn.row_factory = sqlite3.Row
    c = conn.cursor()

    # Fetch order information
    c.execute("SELECT order_id, client_id, order_date, status FROM orders WHERE order_id = ?", (order_id,))
    order_info = c.fetchone()
    if not order_info:
        conn.close()
        return None

    # Check which packed columns exist in order_details
    c.execute("PRAGMA table_info(order_details)")
    columns = [col[1] for col in c.fetchall()]
    has_packed_pcs = 'packed_pcs' in columns
    has_packed_sets = 'packed_sets' in columns
    has_quantity_packed_pcs = 'quantity_packed_pcs' in columns
    has_quantity_packed_sets = 'quantity_packed_sets' in columns

    # Build the select statement for order items
    select_fields = [
        'product_code',
        'quantity_pcs',
        'quantity_sets'
    ]
    if has_packed_pcs and has_packed_sets:
        select_fields += ['packed_pcs', 'packed_sets']
    elif has_quantity_packed_pcs and has_quantity_packed_sets:
        select_fields += ['quantity_packed_pcs', 'quantity_packed_sets']
    else:
        select_fields += ['0 as packed_pcs', '0 as packed_sets']

    c.execute(f"""
        SELECT {', '.join(select_fields)}
        FROM order_details
        WHERE order_id = ?
    """, (order_id,))
    order_items = c.fetchall()

    # Fetch packed products (carton details)
    c.execute("""
        SELECT * FROM packed_products
        WHERE order_id = ?
        ORDER BY carton_number, product_code
    """, (order_id,))
    packed_products = c.fetchall()

    # Fetch carton weights from the carton_weights table
    c.execute("""
        SELECT carton_number, weight
        FROM carton_weights
        WHERE order_id = ?
    """, (order_id,))
    carton_weight_rows = c.fetchall()
    carton_weights = {row['carton_number']: row['weight'] for row in carton_weight_rows}

    conn.close()

    buffer = BytesIO()
    doc = SimpleDocTemplate(
        buffer,
        pagesize=letter,
        rightMargin=0.5*inch,
        leftMargin=0.5*inch,
        topMargin=0.5*inch,
        bottomMargin=0.5*inch
    )

    styles = getSampleStyleSheet()
    
    # Professional title style
    title_style = ParagraphStyle(
        'Title',
        parent=styles['Heading1'],
        fontName='Montserrat-Bold',
        fontSize=28,
        textColor=colors.HexColor("#2c3e50"),
        alignment=1,
        spaceAfter=0.2*inch
    )
    
    header_style = ParagraphStyle(
        'Header',
        parent=styles['Normal'],
        fontName='Montserrat-Bold',
        fontSize=12,
        textColor=colors.HexColor("#34495e")
    )
    
    normal_style = ParagraphStyle(
        'Normal',
        parent=styles['Normal'],
        fontName='Montserrat-Regular',
        fontSize=11,
        textColor=colors.HexColor("#2c3e50")
    )

    elements = []

    # Clean header section
    elements.append(Paragraph(f"INVOICE #{invoice_number}", title_style))
    
    # Invoice info in a clean table
    info_data = [
        [Paragraph("Order Date:", header_style), Paragraph(order_info['order_date'], normal_style)],
        [Paragraph("Client ID:", header_style), Paragraph(order_info['client_id'], normal_style)],
        [Paragraph("Order Status:", header_style), Paragraph(order_info['status'], normal_style)]
    ]
    
    info_table = Table(info_data, colWidths=[2*inch, 4*inch])
    info_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('ALIGN', (1, 0), (1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('BOX', (0, 0), (-1, -1), 1, colors.HexColor("#bdc3c7")),
        ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor("#f8f9fa")),
    ]))
    elements.append(info_table)
    elements.append(Spacer(1, 0.4*inch))

    # Executive Summary (updated with only requested details)
    section_style = ParagraphStyle(
        'Section',
        parent=styles['Heading2'],
        fontName='Montserrat-Bold',
        fontSize=16,
        textColor=colors.HexColor("#2c3e50"),
        spaceAfter=0.2*inch
    )
    
    elements.append(Paragraph("ORDER SUMMARY", section_style))
    summary_table = create_executive_summary(order_items, styles)
    elements.append(summary_table)
    elements.append(Spacer(1, 0.4*inch))

    # ORDER PCS Details Table
    elements.append(Paragraph("ORDER PCS DETAILS", section_style))
    pcs_table = create_pcs_order_table(order_items, styles)
    elements.append(pcs_table)
    elements.append(Spacer(1, 0.4*inch))

    # ORDER SETS Details Table
    elements.append(Paragraph("ORDER SETS DETAILS", section_style))
    sets_table = create_sets_order_table(order_items, styles)
    elements.append(sets_table)

    elements.append(Spacer(1, 0.5*inch))
    elements.append(PageBreak())
    
    # Carton Details Section (unchanged)
    elements.append(Paragraph("CARTON DETAILS", section_style))
    elements.append(Spacer(1, 0.3*inch))

    # Group packed products by carton
    cartons = defaultdict(list)
    for product in packed_products:
        cartons[product['carton_number']].append(product)

    # Check which packed columns exist in packed_products
    if packed_products:
        packed_keys = packed_products[0].keys()
        has_carton_pcs = 'quantity_packed_pcs' in packed_keys
        has_carton_sets = 'quantity_packed_sets' in packed_keys
        has_carton_legacy = 'quantity_packed' in packed_keys
    else:
        has_carton_pcs = has_carton_sets = has_carton_legacy = False

    # Professional carton styling with dark green header
    carton_header_style = ParagraphStyle(
        'CartonHeader', 
        parent=header_style, 
        textColor=colors.white,
        fontSize=12,
        alignment=1,
        fontName='Montserrat-Bold'
    )

    # Display cartons with improved styling
    for carton_number, products in cartons.items():
        weight_value = carton_weights.get(carton_number)
        if weight_value is not None:
            weight_text = f"Weight: {weight_value} kg"
        else:
            weight_text = "Weight: Not specified"
        carton_header_text = f"CARTON {carton_number} - {weight_text}"
        
        carton_data = [[Paragraph(carton_header_text, carton_header_style)]]
        
        if has_carton_pcs or has_carton_sets:
            carton_data.append([
                Paragraph("Product Code", header_style),
                Paragraph("Packed PCS", header_style),
                Paragraph("Packed SETS", header_style)
            ])
            for product in products:
                packed_pcs = product['quantity_packed_pcs'] if has_carton_pcs else 0
                packed_sets = product['quantity_packed_sets'] if has_carton_sets else 0
                carton_data.append([
                    Paragraph(product['product_code'], normal_style),
                    Paragraph(str(packed_pcs), normal_style),
                    Paragraph(str(packed_sets), normal_style)
                ])
            col_widths = [4*inch, 1.5*inch, 1.5*inch]
        elif has_carton_legacy:
            carton_data.append([
                Paragraph("Product Code", header_style),
                Paragraph("Quantity Packed", header_style)
            ])
            for product in products:
                carton_data.append([
                    Paragraph(product['product_code'], normal_style),
                    Paragraph(str(product['quantity_packed']), normal_style)
                ])
            col_widths = [5*inch, 2*inch]
        else:
            carton_data.append([
                Paragraph("Product Code", header_style),
                Paragraph("Quantity", header_style)
            ])
            for product in products:
                carton_data.append([
                    Paragraph(product['product_code'], normal_style),
                    Paragraph("0", normal_style)
                ])
            col_widths = [5*inch, 2*inch]
        
        carton_table = Table(carton_data, colWidths=col_widths)
        carton_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), dark_green),  # Dark green header
            ('SPAN', (0, 0), (-1, 0)),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOX', (0, 0), (-1, -1), 1, colors.HexColor("#bdc3c7")),
            ('INNERGRID', (0, 1), (-1, -1), 0.5, colors.HexColor("#ecf0f1")),
            ('ROWBACKGROUNDS', (0, 2), (-1, -1), [colors.white, colors.HexColor("#f8f9fa")]),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        elements.append(carton_table)
        elements.append(Spacer(1, 0.3*inch))

    # Professional footer
    footer_style = ParagraphStyle(
        'Footer', 
        parent=normal_style, 
        alignment=1, 
        textColor=colors.HexColor("#7f8c8d"),
        fontSize=12,
        fontName='Montserrat-Regular'
    )
    elements.append(Spacer(1, 0.5*inch))
    elements.append(Paragraph("Thank you for your business - Trione Bath Corporation", footer_style))

    # Build the PDF
    doc.build(elements, onFirstPage=add_page_number, onLaterPages=add_page_number)

    buffer.seek(0)
    return buffer

@app.route('/create_new_order', methods=['POST'])
def create_new_order():
    conn = sqlite3.connect('Trione_ERP_Database.db')
    c = conn.cursor()

    try:
        # Retrieve the order data from the request payload
        order_data = request.get_json()

        # Extract required parameters
        original_order_id = order_data.get('original_order_id')
        client_id = order_data.get('client_id')

        # Generate a new order ID using the same logic as /add_order
        c.execute("SELECT MAX(order_id) FROM orders")
        result = c.fetchone()
        new_order_id = 1 if result[0] is None else result[0] + 1

        # Fetch remaining products from the original order
        remaining_products = []
        original_order_date = None
        if original_order_id:
            # Get the original order date
            c.execute("SELECT order_date FROM orders WHERE order_id = ?", (original_order_id,))
            result = c.fetchone()
            if result:
                original_order_date = result[0]  # The original order's date

            # Fetch remaining products
            c.execute('''
                SELECT product_code, 
                       (quantity_pcs - packed_pcs) as remaining_pcs,
                       (quantity_sets - packed_sets) as remaining_sets
                FROM order_details
                WHERE order_id = ? 
                AND (quantity_pcs - packed_pcs > 0 OR quantity_sets - packed_sets > 0)
            ''', (original_order_id,))
            db_products = c.fetchall()

            # Convert database tuples to dictionaries matching the expected format
            remaining_products = [
                {
                    'productCode': product[0],
                    'quantityPcs': product[1],
                    'quantitySets': product[2]
                } for product in db_products if product[1] > 0 or product[2] > 0
            ]

        # Insert the new order
        current_date = datetime.now().strftime('%Y-%m-%d')
        c.execute('''
            INSERT INTO orders
            (order_id, client_id, order_date, status, is_today)
            VALUES (?, ?, ?, ?, ?)
        ''', (new_order_id, client_id, current_date, f'Pending Of {original_order_id}', 0))

        # Insert order details
        for product in remaining_products:
            c.execute('''
                INSERT INTO order_details
                (order_id, product_code, quantity_pcs, quantity_sets, packed_pcs, packed_sets)
                VALUES (?, ?, ?, ?, 0, 0)
            ''', (new_order_id, product['productCode'], product['quantityPcs'], product['quantitySets']))

        # Commit the transaction
        conn.commit()

        try:
            generate_po_for_pending_order(new_order_id, client_id, remaining_products, original_order_date)
            app.logger.info(f"PO generated successfully for order {new_order_id}")
        except Exception as po_error:
            # Log PO generation error but don't fail the entire operation
            app.logger.error(f"Error generating PO for order {new_order_id}: {str(po_error)}")

        # Prepare response
        return jsonify({
            'message': 'Order created successfully',
            'order_id': new_order_id
        }), 200

    except Exception as e:
        conn.rollback()
        # Log the full error for debugging
        app.logger.error(f"Error creating new order: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 400
    finally:
        conn.close()
@app.route('/orders', methods=['GET'])
def get_orders():
    conn = sqlite3.connect('Trione_ERP_Database.db')
    c = conn.cursor()
    c.execute("""
        SELECT o.id, o.order_id, o.client_id, o.status, o.order_date
        FROM orders o
        JOIN order_details od ON o.order_id = od.order_id
        GROUP BY o.id, o.order_id, o.client_id, o.status, o.order_date
    """)
    orders = [{"id": row[0], "orderId": row[1], "clientId": row[2], "status": row[3], "orderdate": row[4]} for row in c.fetchall()]
    conn.close()
    return jsonify(orders)
@app.route('/pack_order', methods=['POST'])
def pack_order():
    data = request.json
    order_id = data.get('orderId')
    invoice_number = data.get('invoiceNumber')
    client_id = data.get('client_id')
    current_date = datetime.now().strftime('%Y-%m-%d')
    print("Received data:", data)
    
    conn = sqlite3.connect('Trione_ERP_Database.db')
    c = conn.cursor()
    
    try:
        # Update the order status
        c.execute("""
            UPDATE orders SET packed_date = ?, status = 'packed' WHERE order_id = ?
        """, (current_date, order_id))
        
        # Use hardcoded phone number
        phone_number = "917984817754"
        
        conn.commit()
        
        # Generate PDF
        combined_pdf_filename = f"Bill No. {invoice_number} Pending and Carton Details.pdf"
        combined_pdf_path = os.path.join(PDF_DIRECTORY, combined_pdf_filename)
        
        # Ensure the PDF directory exists
        os.makedirs(PDF_DIRECTORY, exist_ok=True)
        
        pdf_buffer = generate_combined_order_pdf(order_id, invoice_number)
        with open(combined_pdf_path, 'wb') as f:
            f.write(pdf_buffer.getvalue())
        
        # Send using Node.js script
        try:
            result = subprocess.run([
                'node',
                'send_to_whatsapp_new.js',
                phone_number,
                combined_pdf_path
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                raise Exception(f"WhatsApp send failed: {result.stderr}")
                
        except Exception as e:
            print(f"Error sending WhatsApp: {str(e)}")
            raise e
        
        return jsonify({
            "status": "success",
            "message": "Order packed successfully and PDF sent to WhatsApp",
            "orderId": order_id,
            "combinedPdf": combined_pdf_filename
        })
        
    except sqlite3.Error as e:
        print(f"SQLite error: {str(e)}")
        conn.rollback()
        return jsonify({"status": "error", "message": str(e)}), 500
        
    except Exception as e:
        print(f"General error: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500
        
    finally:
        conn.close()
@app.route('/todays_orders')
def get_todays_orders():
    conn = get_db_connection()
    c = conn.cursor()
    c.execute("""
        SELECT o.order_id, o.client_id, o.status, o.order_date, o.line,
               COUNT(DISTINCT od.product_code) as total_products,
               SUM(od.quantity_to_make) as total_quantity,
               SUM(od.quantity_to_make) - SUM(od.assembled) AS remaining_quantity
        FROM orders o
        LEFT JOIN order_details od ON o.order_id = od.order_id
        WHERE o.is_today = 1
        GROUP BY o.order_id, o.client_id, o.status, o.order_date, o.line
    """)
    orders = [{"orderId": row[0], "clientId": row[1], "status": row[2], "orderdate": row[3],
               "line": row[4], "totalProducts": row[5], "totalQuantity": row[6], "remainingQuantity": row[7]}
              for row in c.fetchall()]
    conn.close()
    return jsonify(orders)

@app.route('/api/todays_orders')
def get_todays_orders_api():
    """Get today's orders with PCS and SETS quantities.

    This endpoint retrieves all orders marked as 'today' with detailed information
    including product counts and quantities in both PCS and SETS units.

    Returns:
        JSON: Array of today's orders with calculated totals
    """
    logger.info("API request received: get_todays_orders_api")

    conn = get_db_connection()
    c = conn.cursor()

    try:
        # Query to get today's orders with product details and totals
        c.execute("""
            WITH OrderTotals AS (
                SELECT
                    od.order_id,
                    COUNT(DISTINCT od.product_code) as total_products,
                    SUM(od.quantity_pcs) as total_quantity_pcs,
                    SUM(od.quantity_sets) as total_quantity_sets,
                    SUM(od.packed_pcs) as total_packed_pcs,
                    SUM(od.packed_sets) as total_packed_sets
                FROM order_details od
                GROUP BY od.order_id
            )
            SELECT
                o.order_id,
                o.client_id,
                o.order_date,
                o.status,
                o.line,
                o.scheduled_date,
                ot.total_products,
                ot.total_quantity_pcs,
                ot.total_quantity_sets,
                ot.total_packed_pcs,
                ot.total_packed_sets
            FROM orders o
            LEFT JOIN OrderTotals ot ON o.order_id = ot.order_id
            WHERE o.is_today = 1
            ORDER BY o.order_date DESC
        """)

        # Fetch results
        orders = c.fetchall()

        # Format the results
        formatted_orders = []
        for order in orders:
            # Calculate total quantities (1 SET = 2 PCS for backward compatibility)
            total_quantity = (order[7] or 0) + ((order[8] or 0) * 2)
            total_packed = (order[9] or 0) + ((order[10] or 0) * 2)

            formatted_orders.append({
                'orderId': order[0],
                'clientId': order[1],
                'orderDate': order[2],
                'status': order[3],
                'line': order[4],
                'scheduledDate': order[5],
                'totalProducts': order[6] or 0,  # Handle NULL values
                'totalQuantityPcs': order[7] or 0,
                'totalQuantitySets': order[8] or 0,
                'totalPackedPcs': order[9] or 0,
                'totalPackedSets': order[10] or 0,
                # For backward compatibility
                'totalQuantity': total_quantity,
                'totalPacked': total_packed,
                'completionPercentage': round((total_packed / total_quantity) * 100) if total_quantity > 0 else 0
            })

        logger.info(f"Returning {len(formatted_orders)} today's orders")
        return jsonify(formatted_orders)

    except sqlite3.Error as e:
        logger.error(f"Database error in get_todays_orders_api: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(e)}"
        }), 500

    except Exception as e:
        logger.error(f"Unexpected error in get_todays_orders_api: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"An unexpected error occurred: {str(e)}"
        }), 500

    finally:
        conn.close()

#==============================================================================
#                         SERVER STARTUP
#==============================================================================
# This section handles starting up the server and initializing everything

def run_server():
    """Run the Flask server with SocketIO.

    This function starts the web server using SocketIO for real-time communication.
    It binds to all network interfaces (0.0.0.0) so it can be accessed from other
    computers on the network, not just localhost.

    The server runs on port 6969 by default.
    """
    port = 6969 #hehe
    host = '0.0.0.0'
    logger.info(f"Starting server on {host}:{port}")
    logger.info("Press Ctrl+C to stop the server")
    socketio.run(app, port=port, host=host)
#==============================================================================
#                         MANUFACTURING FUNCTIONS
#==============================================================================
# These functions handle the manufacturing process tracking system
# They provide functionality for tracking material flow through production stages

#-------------------------------------------------------
# Manufacturing Route Handler Functions
#-------------------------------------------------------

def serve_manufacturing_dashboard():
    """Serve the manufacturing dashboard page."""
    logger.info("Serving manufacturing dashboard page")
    return render_template('manufacturing/dashboard.html')

def serve_scrap_entry_page():
    """Serve the scrap entry page."""
    logger.info("Serving scrap entry page")
    recent_entries = get_recent_scrap_entries()
    return render_template('manufacturing/scrap_entry.html', recent_entries=recent_entries)

def get_scrap_entries_api():
    """API endpoint to retrieve recent scrap entries."""
    entries = get_recent_scrap_entries()
    return jsonify([dict(zip(entry.keys(), entry)) for entry in entries])

def add_scrap_entry_api():
    """API endpoint to add a new scrap entry."""
    data = request.json
    if not all(k in data for k in ['entry_date', 'material_type', 'quantity_kg', 'supplier_name']):
        return jsonify({'status': 'error', 'message': 'Missing required fields'}), 400

    entry_date = parse_date(data['entry_date'])
    material_type = data['material_type']
    quantity_kg = float(data['quantity_kg'])
    supplier_name = data['supplier_name']
    cost_per_kg = data.get('cost_per_kg')

    if cost_per_kg:
        cost_per_kg = float(cost_per_kg)

    if material_type not in ['Brass', 'Zinc']:
        return jsonify({'status': 'error', 'message': "Material type must be either 'Brass' or 'Zinc'"}), 400

    entry_id = add_scrap_entry(entry_date, material_type, quantity_kg, supplier_name, cost_per_kg)

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'Scrap entry added successfully'
    }), 201

def serve_casting_page():
    """Serve the casting page."""
    logger.info("Serving casting page")
    recent_entries = get_recent_casting_entries()
    return render_template('manufacturing/casting.html', recent_entries=recent_entries)

def get_casting_entries_api():
    """API endpoint to retrieve casting entries."""
    entries = get_recent_casting_entries()
    return jsonify([dict(zip(entry.keys(), entry)) for entry in entries])

def add_casting_entry_api():
    """API endpoint to add a new casting entry."""
    data = request.json
    if not all(k in data for k in ['casting_date', 'product_code', 'quantity_pcs', 'quantity_kg']):
        return jsonify({'status': 'error', 'message': 'Missing required fields'}), 400

    casting_date = parse_date(data['casting_date'])
    product_code = data['product_code']
    quantity_pcs = int(data['quantity_pcs'])
    quantity_kg = float(data['quantity_kg'])

    # Calculate per piece weight if not provided (in grams)
    per_piece_weight = data.get('per_piece_weight')
    if not per_piece_weight:
        per_piece_weight = (quantity_kg * 1000) / quantity_pcs if quantity_pcs > 0 else 0
    else:
        per_piece_weight = float(per_piece_weight)

    entry_id = add_casting_entry(casting_date, product_code, quantity_pcs, quantity_kg, per_piece_weight)

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'Casting entry added successfully'
    }), 201

def serve_cnc_page():
    """Serve the CNC page."""
    logger.info("Serving CNC page")
    recent_entries = get_recent_cnc_transactions()
    return render_template('manufacturing/cnc_outward.html', recent_entries=recent_entries)

def get_cnc_transactions_api():
    """API endpoint to retrieve CNC transactions."""
    entries = get_recent_cnc_transactions()
    return jsonify([dict(zip(entry.keys(), entry)) for entry in entries])

def add_cnc_transaction_api():
    """API endpoint to add a new CNC transaction."""
    data = request.json
    if not all(k in data for k in ['transaction_type', 'transaction_date', 'product_code', 'quantity_pcs', 'per_piece_weight']):
        return jsonify({'status': 'error', 'message': 'Missing required fields'}), 400

    transaction_type = data['transaction_type']
    transaction_date = parse_date(data['transaction_date'])
    product_code = data['product_code']
    quantity_pcs = int(data['quantity_pcs'])
    per_piece_weight = float(data['per_piece_weight'])
    quantity_kg = data.get('quantity_kg')

    if quantity_kg:
        quantity_kg = float(quantity_kg)

    if transaction_type not in ['IN', 'OUT']:
        return jsonify({'status': 'error', 'message': "Transaction type must be either 'IN' or 'OUT'"}), 400

    entry_id = add_cnc_transaction(transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight, quantity_kg)

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'CNC transaction added successfully'
    }), 201

def serve_primary_store_page():
    """Serve the primary store page."""
    logger.info("Serving primary store page")
    inventory = get_current_primary_inventory()
    return render_template('manufacturing/primary_store_receipt.html', inventory=inventory)

def get_primary_store_inventory_api():
    """API endpoint to retrieve primary store inventory."""
    inventory = get_current_primary_inventory()
    return jsonify([dict(zip(item.keys(), item)) for item in inventory])

def add_primary_store_transaction_api():
    """API endpoint to add a new primary store transaction."""
    data = request.json
    if not all(k in data for k in ['transaction_type', 'transaction_date', 'product_code', 'quantity_pcs', 'per_piece_weight']):
        return jsonify({'status': 'error', 'message': 'Missing required fields'}), 400

    transaction_type = data['transaction_type']
    transaction_date = parse_date(data['transaction_date'])
    product_code = data['product_code']
    quantity_pcs = int(data['quantity_pcs'])
    per_piece_weight = float(data['per_piece_weight'])

    if transaction_type not in ['IN', 'OUT']:
        return jsonify({'status': 'error', 'message': "Transaction type must be either 'IN' or 'OUT'"}), 400

    entry_id = add_primary_store_transaction(transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight)

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'Primary store transaction added successfully'
    }), 201

def serve_buffing_page():
    """Serve the buffing page."""
    logger.info("Serving buffing page")
    recent_entries = get_recent_buffing_transactions()
    return render_template('manufacturing/buffing_outward.html', recent_entries=recent_entries)

def get_buffing_transactions_api():
    """API endpoint to retrieve buffing transactions."""
    entries = get_recent_buffing_transactions()
    return jsonify([dict(zip(entry.keys(), entry)) for entry in entries])

def add_buffing_transaction_api():
    """API endpoint to add a new buffing transaction."""
    data = request.json
    if not all(k in data for k in ['transaction_type', 'transaction_date', 'product_code', 'quantity_pcs', 'per_piece_weight']):
        return jsonify({'status': 'error', 'message': 'Missing required fields'}), 400

    transaction_type = data['transaction_type']
    transaction_date = parse_date(data['transaction_date'])
    product_code = data['product_code']
    quantity_pcs = int(data['quantity_pcs'])
    per_piece_weight = float(data['per_piece_weight'])
    material_loss = data.get('material_loss')

    if material_loss:
        material_loss = float(material_loss)

    if transaction_type not in ['IN', 'OUT']:
        return jsonify({'status': 'error', 'message': "Transaction type must be either 'IN' or 'OUT'"}), 400

    entry_id = add_buffing_transaction(transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight, material_loss)

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'Buffing transaction added successfully'
    }), 201

def serve_final_store_page():
    """Serve the final store page."""
    logger.info("Serving final store page")
    inventory = get_current_final_inventory()
    return render_template('manufacturing/final_store_transaction.html', inventory=inventory)

def get_final_store_inventory_api():
    """API endpoint to retrieve final store inventory."""
    inventory = get_current_final_inventory()
    return jsonify([dict(zip(item.keys(), item)) for item in inventory])

def add_final_store_transaction_api():
    """API endpoint to add a new final store transaction."""
    data = request.json
    if not all(k in data for k in ['transaction_type', 'transaction_date', 'product_code', 'quantity_pcs', 'per_piece_weight']):
        return jsonify({'status': 'error', 'message': 'Missing required fields'}), 400

    transaction_type = data['transaction_type']
    transaction_date = parse_date(data['transaction_date'])
    product_code = data['product_code']
    quantity_pcs = int(data['quantity_pcs'])
    per_piece_weight = float(data['per_piece_weight'])
    destination = data.get('destination')
    order_id = data.get('order_id')

    if order_id:
        order_id = int(order_id)

    if transaction_type not in ['IN', 'OUT']:
        return jsonify({'status': 'error', 'message': "Transaction type must be either 'IN' or 'OUT'"}), 400

    entry_id = add_final_store_transaction(transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight, destination, order_id)

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'Final store transaction added successfully'
    }), 201

def serve_products_page():
    """Serve the products management page."""
    logger.info("Serving products page")
    return render_template('manufacturing/products.html')

def get_products_api():
    """API endpoint to retrieve products."""
    products = get_all_products()
    return jsonify([dict(zip(product.keys(), product)) for product in products])

def add_product_api():
    """API endpoint to add a new product.

    This endpoint accepts both JSON and form data (multipart/form-data) for product creation.
    It handles file uploads for product images when using form data.
    """
    try:
        # Check if the request is JSON or form data
        if request.is_json:
            # Handle JSON data
            data = request.get_json()
            product_code = data.get('product_code')
            product_name = data.get('product_name')
            material_type = data.get('material_type')
            product_weight = data.get('product_weight')
            product_description = data.get('product_description')
        else:
            # Handle form data
            product_code = request.form.get('product_code')
            product_name = request.form.get('product_name')
            material_type = request.form.get('material_type')
            product_weight = request.form.get('product_weight')
            product_description = request.form.get('product_description')

            # Handle file upload if present
            if 'product_image' in request.files:
                product_image = request.files['product_image']
                if product_image.filename:
                    # Ensure the product_images directory exists
                    product_images_dir = os.path.join('static', 'product_images')
                    if not os.path.exists(product_images_dir):
                        os.makedirs(product_images_dir)
                        logger.info(f"Created directory: {product_images_dir}")

                    # Save the image to the static/product_images folder
                    image_filename = secure_filename(f"{product_code}_{product_image.filename}")
                    image_path = os.path.join(product_images_dir, image_filename)
                    product_image.save(image_path)
                    logger.info(f"Saved product image to {image_path}")

        if not product_code:
            return jsonify({'message': 'Product code is required'}), 400

        # Create specifications JSON
        specifications = {}
        if product_weight:
            specifications['weight'] = float(product_weight) if product_weight else None

        # Add product to database
        # The add_product function expects (product_code, description, category, material_type, specifications, image)
        # Use product_description if available, otherwise use product_name as description
        description = product_description if product_description else product_name
        add_product(product_code, description, None, material_type, specifications, None)

        return jsonify({'message': 'Product added successfully'}), 201
    except Exception as e:
        logger.error(f"Error adding product: {str(e)}")
        return jsonify({'message': f'Error adding product: {str(e)}'}), 500

def serve_manufacturing_reports_page():
    """Serve the manufacturing reports page."""
    logger.info("Serving manufacturing reports page")
    return render_template('manufacturing/reports.html')

def get_material_loss_report_api():
    """API endpoint to retrieve material loss report."""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    product_code = request.args.get('product_code')

    if start_date:
        start_date = parse_date(start_date)

    if end_date:
        end_date = parse_date(end_date)

    report = get_material_loss_report(start_date, end_date, product_code)

    return jsonify([dict(zip(row.keys(), row)) for row in report])

def get_production_history_api(product_code):
    """API endpoint to retrieve production history for a product."""
    history = get_production_history(product_code)

    return jsonify([dict(zip(entry.keys(), entry)) for entry in history])

def add_scrap_entry(entry_date, material_type, quantity_kg, supplier_name, cost_per_kg=None):
    """Add a new scrap material entry."""
    conn = get_db_connection()
    cursor = conn.cursor()

    total_cost = None
    if cost_per_kg:
        total_cost = cost_per_kg * quantity_kg

    cursor.execute('''
    INSERT INTO scrap_entry (entry_date, material_type, quantity_kg, supplier_name, cost_per_kg, total_cost)
    VALUES (?, ?, ?, ?, ?, ?)
    ''', (entry_date, material_type, quantity_kg, supplier_name, cost_per_kg, total_cost))

    conn.commit()
    entry_id = cursor.lastrowid
    conn.close()

    return entry_id

def get_recent_scrap_entries(limit=20):
    """Get the most recent scrap entries."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT id, entry_date, material_type, quantity_kg, supplier_name, cost_per_kg, total_cost
    FROM scrap_entry
    ORDER BY entry_date DESC, id DESC
    LIMIT ?
    ''', (limit,))

    entries = cursor.fetchall()
    conn.close()

    return entries

def add_product(product_code, description=None, category=None, material_type='Brass', specifications=None, image=None):
    """Add a new product to the product master.

    Args:
        product_code (str): The unique code for the product
        description (str, optional): Product description
        category (str, optional): Product category
        material_type (str, optional): Material type, defaults to 'Brass'
        specifications (dict, optional): Product specifications as a dictionary
        image (str, optional): Path to product image

    Returns:
        str: The product code of the added product
    """
    conn = get_db_connection()
    cursor = conn.cursor()

    # Convert specifications dictionary to JSON string if it's a dictionary
    if specifications and isinstance(specifications, dict):
        specifications = json.dumps(specifications)

    cursor.execute('''
    INSERT INTO product_master (product_code, description, category, material_type, specifications, image)
    VALUES (?, ?, ?, ?, ?, ?)
    ''', (product_code, description, category, material_type, specifications, image))

    conn.commit()
    conn.close()

    return product_code

def add_casting_entry(casting_date, product_code, quantity_pcs, quantity_kg, per_piece_weight):
    """Add a new casting production record."""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Check if product exists, if not add it
    cursor.execute('SELECT 1, material_type FROM product_master WHERE product_code = ?', (product_code,))
    product = cursor.fetchone()
    if not product:
        # Default to Brass if product doesn't exist
        add_product(product_code)
        material_type = 'Brass'
    else:
        material_type = product['material_type']

    # Insert into casting table
    cursor.execute('''
    INSERT INTO casting (casting_date, product_code, quantity_pcs, quantity_kg, per_piece_weight)
    VALUES (?, ?, ?, ?, ?)
    ''', (casting_date, product_code, quantity_pcs, quantity_kg, per_piece_weight))

    # Subtract from scrap inventory by creating a negative entry
    # We use "Casting Consumption" as the supplier name to indicate this is not a purchase but a consumption
    cursor.execute('''
    INSERT INTO scrap_entry (entry_date, material_type, quantity_kg, supplier_name, cost_per_kg, total_cost)
    VALUES (?, ?, ?, ?, NULL, NULL)
    ''', (casting_date, material_type, -quantity_kg, "Casting Consumption"))

    conn.commit()
    entry_id = cursor.lastrowid
    conn.close()

    return entry_id

def get_recent_casting_entries(limit=20):
    """Get the most recent casting entries."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT c.id, c.casting_date, c.product_code, c.quantity_pcs, c.quantity_kg, c.per_piece_weight,
           p.description
    FROM casting c
    LEFT JOIN product_master p ON c.product_code = p.product_code
    ORDER BY c.casting_date DESC, c.id DESC
    LIMIT ?
    ''', (limit,))

    entries = cursor.fetchall()
    conn.close()

    return entries

def add_cnc_transaction(transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight, quantity_kg=None):
    """Add a CNC transaction (IN or OUT)."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    INSERT INTO cnc_transaction (transaction_type, transaction_date, product_code, quantity_pcs, quantity_kg, per_piece_weight)
    VALUES (?, ?, ?, ?, ?, ?)
    ''', (transaction_type, transaction_date, product_code, quantity_pcs, quantity_kg, per_piece_weight))

    # If this is an OUT transaction, automatically create an IN transaction for the primary store
    if transaction_type == 'OUT':
        cursor.execute('''
        INSERT INTO primary_store (transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight)
        VALUES (?, ?, ?, ?, ?)
        ''', ('IN', transaction_date, product_code, quantity_pcs, per_piece_weight))

    conn.commit()
    entry_id = cursor.lastrowid
    conn.close()

    return entry_id

def get_recent_cnc_transactions(limit=20):
    """Get the most recent CNC transactions."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT c.id, c.transaction_type, c.transaction_date, c.product_code, c.quantity_pcs, c.quantity_kg, c.per_piece_weight,
           p.description
    FROM cnc_transaction c
    LEFT JOIN product_master p ON c.product_code = p.product_code
    ORDER BY c.transaction_date DESC, c.id DESC
    LIMIT ?
    ''', (limit,))

    entries = cursor.fetchall()
    conn.close()

    return entries

def add_primary_store_transaction(transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight):
    """Add a primary store transaction (IN or OUT)."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    INSERT INTO primary_store (transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight)
    VALUES (?, ?, ?, ?, ?)
    ''', (transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight))

    # If this is an OUT transaction, automatically create an IN transaction for buffing
    if transaction_type == 'OUT':
        cursor.execute('''
        INSERT INTO buffing_transaction (transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight, material_loss)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', ('IN', transaction_date, product_code, quantity_pcs, per_piece_weight, 0))

    conn.commit()
    entry_id = cursor.lastrowid
    conn.close()

    return entry_id

def get_current_primary_inventory():
    """Get current inventory levels in the primary store."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT p.product_code, p.current_stock, p.avg_piece_weight, pm.description
    FROM primary_store_inventory p
    LEFT JOIN product_master pm ON p.product_code = pm.product_code
    WHERE p.current_stock > 0
    ORDER BY p.product_code
    ''')

    inventory = cursor.fetchall()
    conn.close()

    return inventory

def add_buffing_transaction(transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight, material_loss=None):
    """Add a buffing transaction (IN or OUT)."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    INSERT INTO buffing_transaction (transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight, material_loss)
    VALUES (?, ?, ?, ?, ?, ?)
    ''', (transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight, material_loss))

    # If this is an OUT transaction, automatically create an IN transaction for final store
    if transaction_type == 'OUT':
        cursor.execute('''
        INSERT INTO final_store (transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight, destination, order_id)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', ('IN', transaction_date, product_code, quantity_pcs, per_piece_weight, None, None))

    conn.commit()
    entry_id = cursor.lastrowid
    conn.close()

    return entry_id

def get_recent_buffing_transactions(limit=20):
    """Get the most recent buffing transactions."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT b.id, b.transaction_type, b.transaction_date, b.product_code, b.quantity_pcs, b.per_piece_weight, b.material_loss,
           p.description
    FROM buffing_transaction b
    LEFT JOIN product_master p ON b.product_code = p.product_code
    ORDER BY b.transaction_date DESC, b.id DESC
    LIMIT ?
    ''', (limit,))

    entries = cursor.fetchall()
    conn.close()

    return entries

def add_final_store_transaction(transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight, destination=None, order_id=None):
    """Add a final store transaction (IN or OUT)."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    INSERT INTO final_store_transaction (transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight, destination, order_id)
    VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (transaction_type, transaction_date, product_code, quantity_pcs, per_piece_weight, destination, order_id))

    # Only create shipping record if destination is provided
    if transaction_type == 'OUT' and destination:
        cursor.execute('''
        INSERT INTO shipping (shipping_date, order_id, product_code, quantity_pcs)
        VALUES (?, ?, ?, ?)
        ''', (transaction_date, order_id, product_code, quantity_pcs))

    conn.commit()
    entry_id = cursor.lastrowid
    conn.close()

    return entry_id

def get_current_final_inventory():
    """Get current inventory levels in the final store."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT f.product_code, f.current_stock, f.avg_piece_weight, pm.description
    FROM final_store_inventory f
    LEFT JOIN product_master pm ON f.product_code = pm.product_code
    WHERE f.current_stock > 0
    ORDER BY f.product_code
    ''')

    inventory = cursor.fetchall()
    conn.close()

    return inventory

def get_recent_final_store_transactions(limit=20):
    """Get the most recent final store transactions."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT f.id, f.transaction_type, f.transaction_date, f.product_code, f.quantity_pcs, f.per_piece_weight, f.destination, f.order_id,
           p.description
    FROM final_store_transaction f
    LEFT JOIN product_master p ON f.product_code = p.product_code
    ORDER BY f.transaction_date DESC, f.id DESC
    LIMIT ?
    ''', (limit,))

    entries = cursor.fetchall()
    conn.close()

    return entries

def get_all_products():
    """Get all products."""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT product_code, name, description, casting_weight, machining_weight, final_weight, photo_filename
    FROM product_master
    ORDER BY product_code
    ''')

    products = cursor.fetchall()
    conn.close()

    return [{
        'product_code': row['product_code'],
        'name': row['name'],
        'description': row['description'],
        'casting_weight': row['casting_weight'],
        'machining_weight': row['machining_weight'],
        'final_weight': row['final_weight'],
        'photo_filename': row['photo_filename']
    } for row in products]

def get_material_loss_report(start_date=None, end_date=None, product_code=None):
    """Get a report on material loss through the production process."""
    conn = get_db_connection()
    cursor = conn.cursor()

    query = '''
    SELECT * FROM material_loss_analysis
    WHERE 1=1
    '''
    params = []

    if start_date:
        query += ' AND casting_date >= ?'
        params.append(start_date)

    if end_date:
        query += ' AND casting_date <= ?'
        params.append(end_date)

    if product_code:
        query += ' AND product_code = ?'
        params.append(product_code)

    query += ' ORDER BY casting_date DESC, product_code'

    cursor.execute(query, params)
    report = cursor.fetchall()
    conn.close()

    return report

def get_production_history(product_code):
    """Get the complete production history for a specific product."""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Casting history
    cursor.execute('''
    SELECT 'casting' as stage, casting_date as transaction_date, quantity_pcs, quantity_kg, per_piece_weight
    FROM casting
    WHERE product_code = ?
    ''', (product_code,))

    casting_history = cursor.fetchall()

    # CNC history
    cursor.execute('''
    SELECT 'cnc_' || transaction_type as stage, transaction_date, quantity_pcs, quantity_kg, per_piece_weight
    FROM cnc_transaction
    WHERE product_code = ?
    ''', (product_code,))

    cnc_history = cursor.fetchall()

    # Primary store history
    cursor.execute('''
    SELECT 'primary_' || transaction_type as stage, transaction_date, quantity_pcs, NULL as quantity_kg, per_piece_weight
    FROM primary_store
    WHERE product_code = ?
    ''', (product_code,))

    primary_history = cursor.fetchall()

    # Buffing history
    cursor.execute('''
    SELECT 'buffing_' || transaction_type as stage, transaction_date, quantity_pcs, NULL as quantity_kg, per_piece_weight
    FROM buffing_transaction
    WHERE product_code = ?
    ''', (product_code,))

    buffing_history = cursor.fetchall()

    # Final store history
    cursor.execute('''
    SELECT 'final_' || transaction_type as stage, transaction_date, quantity_pcs, NULL as quantity_kg, per_piece_weight
    FROM final_store_transaction
    WHERE product_code = ?
    ''', (product_code,))

    final_history = cursor.fetchall()

    conn.close()

    # Combine all histories
    all_history = casting_history + cnc_history + primary_history + buffing_history + final_history
    # Sort by date
    all_history.sort(key=lambda x: x['transaction_date'])

    return all_history
@app.route('/update_carton_weight', methods=['POST'])
@app.route('/cancel_order/<int:order_id>', methods=['POST'])
def cancel_order(order_id):
    """Cancel an order by setting its status to 'CANCELLED'."""
    try:
        conn = get_db_connection()
        c = conn.cursor()
        c.execute("UPDATE orders SET status = ? WHERE order_id = ?", ('CANCELLED', order_id))
        conn.commit()
        updated = c.rowcount
        conn.close()
        if updated:
            return jsonify({'status': 'success', 'message': f'Order {order_id} cancelled.'}), 200
        else:
            return jsonify({'status': 'error', 'message': f'Order {order_id} not found.'}), 404
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500
@app.route('/update_todays_orders', methods=['POST'])
def update_todays_orders():
    """Update the 'is_today' flag for multiple orders.

    This endpoint receives a list of order IDs that should be marked as today's orders.
    Any pending orders not in this list will have their 'is_today' flag set to 0.

    Request body: {'orders': [{'orderId': 123}, {'orderId': 456}, ...]}
    """
    try:
        data = request.get_json()
        order_updates = data.get('orders', [])

        if not isinstance(order_updates, list):
            return jsonify({'status': 'error', 'message': 'Orders must be provided as a list'}), 400

        # Extract order IDs from the request
        today_order_ids = [update.get('orderId') for update in order_updates if update.get('orderId')]

        conn = get_db_connection()
        cursor = conn.cursor()

        # First set is_today=0 for all pending orders (not affecting completed/cancelled)
        cursor.execute("UPDATE orders SET is_today = 0 WHERE status = 'Pending'")

        # Then set is_today=1 for the orders in the provided list
        if today_order_ids:
            placeholders = ','.join(['?'] * len(today_order_ids))
            cursor.execute(f"UPDATE orders SET is_today = 1 WHERE order_id IN ({placeholders})", today_order_ids)

        conn.commit()
        conn.close()

        return jsonify({
            'status': 'success',
            'message': f"Updated {len(today_order_ids)} orders to 'today' status."
        })
    except Exception as e:
        logger.error(f"Error updating today's orders: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/order/<int:order_id>/today', methods=['POST'])
def update_order_today_status(order_id):
    """Update the 'is_today' flag for a single order.

    Request body: {'isToday': true/false}
    """
    try:
        data = request.get_json()
        is_today = 1 if data.get('isToday') else 0

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("UPDATE orders SET is_today = ? WHERE order_id = ?", (is_today, order_id))
        conn.commit()
        conn.close()

        return jsonify({
            'status': 'success',
            'message': f"Order {order_id} updated: is_today={is_today}"
        })
    except Exception as e:
        logger.error(f"Error updating order {order_id} today status: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/order/<int:order_id>/scheduled_date', methods=['POST'])
def update_order_scheduled_date(order_id):
    """Update the scheduled date for an order.

    Request body: {'scheduledDate': 'YYYY-MM-DD'}
    """
    try:
        data = request.get_json()
        scheduled_date = data.get('scheduledDate')

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("UPDATE orders SET scheduled_date = ? WHERE order_id = ?", (scheduled_date, order_id))
        conn.commit()
        conn.close()

        return jsonify({
            'status': 'success',
            'message': f"Order {order_id} scheduled date updated"
        })
    except Exception as e:
        logger.error(f"Error updating scheduled date for order {order_id}: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/order/<int:order_id>/mark_packed', methods=['POST'])
def mark_order_as_packed(order_id):
    """Hearken, ye noble packagers! This mystical endpoint doth mark an order as 'Packed',
    signifying that all products within have been carefully arranged into cartons,
    ready for their journey to the client's realm.

    Like a royal decree changing the status of a kingdom's citizen, this function
    updates the sacred 'status' field in the orders table, proclaiming to all
    that the packing quest hath been completed successfully.

    Args:
        order_id (int): The ID of the order to be marked as packed

    Query Parameters:
        force (bool): If true, mark the order as packed even if not all products are packed

    Returns:
        JSON: A proclamation of success or failure, wrapped in the mystical format of JSON
    """
    logger.info(f"API request received: mark_order_as_packed for order {order_id}")

    # Check if we should force mark as packed
    force = request.args.get('force', 'false').lower() == 'true'

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # First check if the order exists
        cursor.execute("SELECT status FROM orders WHERE order_id = ?", (order_id,))
        result = cursor.fetchone()

        if not result:
            logger.warning(f"Order {order_id} not found")
            conn.close()
            return jsonify({
                'status': 'error',
                'message': f"Order {order_id} not found"
            }), 404

        current_status = result[0]

        # Check if all products have been packed (only if not forcing)
        if not force:
            cursor.execute("""
                SELECT
                    SUM(quantity_pcs) as total_pcs,
                    SUM(quantity_sets) as total_sets,
                    SUM(packed_pcs) as packed_pcs,
                    SUM(packed_sets) as packed_sets
                FROM order_details
                WHERE order_id = ?
            """, (order_id,))

            quantities = cursor.fetchone()
            total_pcs = quantities[0] or 0
            total_sets = quantities[1] or 0
            packed_pcs = quantities[2] or 0
            packed_sets = quantities[3] or 0

            # Check if all products are packed
            all_packed = (packed_pcs >= total_pcs) and (packed_sets >= total_sets)

            if not all_packed:
                logger.warning(f"Cannot mark order {order_id} as packed - not all products are packed")
                conn.close()
                return jsonify({
                    'status': 'warning',
                    'message': f"Not all products are packed. PCS: {packed_pcs}/{total_pcs}, SETS: {packed_sets}/{total_sets}",
                    'proceed': False
                })

        # Update the order status
        cursor.execute("UPDATE orders SET status = 'Packed' WHERE order_id = ?", (order_id,))
        conn.commit()

        if force:
            logger.info(f"Order {order_id} force-marked as Packed (previous status: {current_status})")
        else:
            logger.info(f"Order {order_id} marked as Packed (previous status: {current_status})")

        conn.close()

        return jsonify({
            'status': 'success',
            'message': f"Order {order_id} marked as Packed",
            'previousStatus': current_status
        })

    except Exception as e:
        logger.error(f"Error marking order {order_id} as packed: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/manufacturing/api/buffing/transactions', methods=['GET'])
def manufacturing_get_buffing_transactions_api():
    """API endpoint to retrieve recent buffing transactions."""
    limit = request.args.get('limit', 20, type=int)
    entries = get_recent_buffing_transactions(limit)
    return jsonify([dict(zip(entry.keys(), entry)) for entry in entries])

@app.route('/manufacturing/api/buffing/wip', methods=['GET'])
def manufacturing_get_buffing_wip_api():
    """API endpoint to retrieve buffing work in progress."""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Query to calculate WIP based on IN and OUT transactions
    cursor.execute('''
    WITH buffing_in AS (
        SELECT
            product_code,
            SUM(quantity_pcs) as received_count,
            MIN(transaction_date) as first_receipt_date,
            AVG(per_piece_weight) as per_piece_weight
        FROM buffing_transaction
        WHERE transaction_type = 'IN'
        GROUP BY product_code
    ),
    buffing_out AS (
        SELECT
            product_code,
            SUM(quantity_pcs) as completed_count
        FROM buffing_transaction
        WHERE transaction_type = 'OUT'
        GROUP BY product_code
    ),
    product_descriptions AS (
        SELECT
            product_code,
            description
        FROM product_master
    )
    SELECT
        b_in.product_code,
        pd.description,
        b_in.first_receipt_date as received_date,
        IFNULL(b_in.received_count, 0) - IFNULL(b_out.completed_count, 0) as quantity_pcs,
        b_in.per_piece_weight,
        JULIANDAY('now') - JULIANDAY(b_in.first_receipt_date) as days_in_wip
    FROM
        buffing_in b_in
    LEFT JOIN
        buffing_out b_out ON b_in.product_code = b_out.product_code
    LEFT JOIN
        product_descriptions pd ON b_in.product_code = pd.product_code
    WHERE
        IFNULL(b_in.received_count, 0) - IFNULL(b_out.completed_count, 0) > 0
    ORDER BY
        days_in_wip DESC
    ''')

    wip_items = cursor.fetchall()
    conn.close()

    return jsonify([dict(zip(item.keys(), item)) for item in wip_items])

@app.route('/manufacturing/api/primary-store/transactions', methods=['GET'])
def manufacturing_get_primary_store_transactions_api():
    """API endpoint to retrieve primary store transactions."""
    limit = request.args.get('limit', 20, type=int)

    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT
        ps.*,
        pm.description
    FROM
        primary_store ps
    LEFT JOIN
        product_master pm ON ps.product_code = pm.product_code
    ORDER BY
        ps.transaction_date DESC, ps.id DESC
    LIMIT ?
    ''', (limit,))

    entries = cursor.fetchall()
    conn.close()

    return jsonify([dict(zip(entry.keys(), entry)) for entry in entries])

@app.route('/manufacturing/api/final_store/transactions', methods=['GET'])
def manufacturing_get_final_store_transactions_api():
    """API endpoint to retrieve recent final store transactions."""
    limit = request.args.get('limit', 20, type=int)
    entries = get_recent_final_store_transactions(limit)
    return jsonify([dict(zip(entry.keys(), entry)) for entry in entries])

@app.route('/manufacturing/api/final_store/in', methods=['POST'])
def manufacturing_add_final_store_in_transaction():
    """API endpoint to add a new final store IN transaction."""
    # Import the blueprint function
    from manufacturing_routes import add_final_store_in_api
    return add_final_store_in_api()

@app.route('/manufacturing/api/final_store/out', methods=['POST'])
def manufacturing_add_final_store_out_transaction():
    """API endpoint to add a new final store OUT transaction."""
    # Import the blueprint function
    from manufacturing_routes import add_final_store_out_api
    return add_final_store_out_api()

@app.route('/manufacturing/api/product-codes', methods=['GET'])
def get_product_codes():
    """Get all product codes for dropdown menus"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT product_code, description, material_type, specifications
    FROM product_master
    ORDER BY product_code
    ''')

    products = cursor.fetchall()
    conn.close()

    return jsonify([dict(zip(product.keys(), product)) for product in products])
@app.route('/manufacturing/inventory')
def manufacturing_inventory():
    """Serve the comprehensive inventory page."""
    logger.info("Serving manufacturing inventory page")
    return render_template('manufacturing/inventory.html')

@app.route('/manufacturing/api/inventory/all', methods=['GET'])
def get_all_inventory():
    """Get inventory data from all stores."""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Get all products first
    cursor.execute('''
    SELECT product_code, description 
    FROM product_master
    ORDER BY product_code
    ''')
    
    products = cursor.fetchall()
    
    # Get final store inventory
    cursor.execute('''
    SELECT product_code, current_stock 
    FROM final_store_inventory
    ''')
    
    final_store = {row['product_code']: row['current_stock'] for row in cursor.fetchall()}
    
    # Get primary store inventory
    cursor.execute('''
    SELECT product_code, current_stock 
    FROM primary_store_inventory
    ''')
    
    primary_store = {row['product_code']: row['current_stock'] for row in cursor.fetchall()}
    
    # Initialize buffing WIP dict
    buffing_wip = {}
    
    # Try to get buffing WIP (in-progress work)
    try:
        # This is a simplified query - adjust based on your actual schema
        cursor.execute('''
        SELECT product_code, SUM(quantity_pcs) as wip_qty
        FROM buffing_wip
        GROUP BY product_code
        ''')
        
        buffing_result = cursor.fetchall()
        if buffing_result:
            buffing_wip = {row['product_code']: row['wip_qty'] for row in buffing_result}
    except Exception as e:
        logger.error(f"Error getting buffing WIP: {str(e)}")
    
    # Combine all data
    inventory_data = []
    for product in products:
        product_code = product['product_code']
        inventory_data.append({
            'product_code': product_code,
            'description': product['description'],
            'final_store': final_store.get(product_code, 0),
            'primary_store': primary_store.get(product_code, 0),
            'buffing_wip': buffing_wip.get(product_code, 0),
            'total': (final_store.get(product_code, 0) + 
                     primary_store.get(product_code, 0) + 
                     buffing_wip.get(product_code, 0))
        })
    
    conn.close()
    return jsonify(inventory_data)

@app.route('/api/available_products/<int:order_id>', methods=['GET'])
def get_available_products(order_id):
    """Get available products for an order that can be added to cartons.

    This endpoint retrieves a list of products for a specific order that have
    remaining quantities available to be packed. It calculates the difference
    between ordered quantities and packed quantities for both PCS and SETS.

    Args:
        order_id (int): The ID of the order to check

    Returns:
        JSON: Array of products with remaining quantities
    """
    logger.info(f"API request received: get_available_products for order {order_id}")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # First verify the order exists
        cursor.execute("SELECT COUNT(*) FROM orders WHERE order_id = ?", (order_id,))
        if cursor.fetchone()[0] == 0:
            logger.warning(f"Order {order_id} not found")
            conn.close()
            return jsonify({
                "status": "error",
                "message": f"Order {order_id} not found"
            }), 404
        
        # Get products with remaining quantities
        cursor.execute("""
            SELECT od.product_code, od.quantity_pcs, od.quantity_sets, 
                   od.packed_pcs, od.packed_sets
            FROM order_details od
            WHERE od.order_id = ?
        """, (order_id,))
        
        products = []
        for row in cursor.fetchall():
            product_code, quantity_pcs, quantity_sets, packed_pcs, packed_sets = row
            
            # Calculate remaining quantities
            remaining_pcs = quantity_pcs - packed_pcs
            remaining_sets = quantity_sets - packed_sets
            
            # Include product if it has any remaining quantity
            if remaining_pcs > 0 or remaining_sets > 0:
                products.append({
                    "productCode": product_code,
                    "remainingPcs": remaining_pcs,
                    "remainingSets": remaining_sets,
                    # For backward compatibility
                    "remainingQuantity": remaining_pcs + (remaining_sets * 2)  # 1 SET = 2 PCS
                })
        
        logger.info(f"Returning {len(products)} available products for order {order_id}")
        return jsonify(products)
        
    except sqlite3.Error as e:
        logger.error(f"Database error in get_available_products: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Database error: {str(e)}"
        }), 500
        
    except Exception as e:
        logger.error(f"Unexpected error in get_available_products: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Unexpected error: {str(e)}"
        }), 500
        
    finally:
        conn.close()

@app.route('/delete_carton', methods=['POST'])
def delete_carton():
    """Delete a carton and all products within it from an order.

    This endpoint removes a carton and all its products for a specific order.
    It updates the order_details table to reflect the reduction in packed quantities.

    Request body should contain:
    - order_id: The ID of the order
    - carton_number: The carton number to delete

    Returns:
        JSON: Success or error message
    """
    logger.info("API request received: delete_carton")
    
    data = request.get_json()
    
    # Check for required parameters
    if not data or 'order_id' not in data or 'carton_number' not in data:
        logger.error("Missing required parameters in delete_carton")
        return jsonify({
            'status': 'error',
            'message': 'order_id and carton_number are required'
        }), 400
    
    order_id = int(data['order_id'])
    carton_number = int(data['carton_number'])
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # First get all products in this carton
        cursor.execute("""
            SELECT product_code, quantity_packed_pcs, quantity_packed_sets
            FROM packed_products
            WHERE order_id = ? AND carton_number = ?
        """, (order_id, carton_number))
        
        products = cursor.fetchall()
        if not products:
            logger.warning(f"Carton {carton_number} not found for order {order_id}")
            conn.close()
            return jsonify({
                'status': 'error',
                'message': f"Carton {carton_number} not found for order {order_id}"
            }), 404
        
        # Update order_details to reduce packed quantities for each product
        for product in products:
            product_code, quantity_packed_pcs, quantity_packed_sets = product
            
            cursor.execute("""
                UPDATE order_details
                SET packed_pcs = packed_pcs - ?, packed_sets = packed_sets - ?
                WHERE order_id = ? AND product_code = ?
            """, (quantity_packed_pcs, quantity_packed_sets, order_id, product_code))
        
        # Delete all products from the carton
        cursor.execute("""
            DELETE FROM packed_products
            WHERE order_id = ? AND carton_number = ?
        """, (order_id, carton_number))
        
        # Delete the carton weight if it exists
        cursor.execute("""
            DELETE FROM carton_weights
            WHERE order_id = ? AND carton_number = ?
        """, (order_id, carton_number))
        
        conn.commit()
        
        logger.info(f"Deleted carton {carton_number} with {len(products)} products for order {order_id}")
        return jsonify({
            'status': 'success',
            'message': 'Carton deleted successfully',
            'orderId': order_id,
            'cartonNumber': carton_number,
            'productsRemoved': len(products)
        })
        
    except sqlite3.Error as e:
        conn.rollback()
        logger.error(f"Database error in delete_carton: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Database error: {str(e)}"
        }), 500
        
    except Exception as e:
        conn.rollback()
        logger.error(f"Unexpected error in delete_carton: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Unexpected error: {str(e)}"
        }), 500
        
    finally:
        conn.close()

@app.route('/delete_carton_product', methods=['POST'])
def delete_carton_product():
    """Remove a product from a carton and update order details.

    This endpoint removes a product from a carton and updates the packed counts
    in the order_details table accordingly. It handles both PC and SET quantities.

    Request body should contain:
    - orderId: The ID of the order
    - cartonNumber: The carton number
    - productCode: The product code to remove

    Returns:
        JSON: Success or error message
    """
    logger.info("API request received: delete_carton_product")
    
    data = request.get_json()
    
    # Check for required parameters
    if not data or 'orderId' not in data or 'cartonNumber' not in data or 'productCode' not in data:
        logger.error("Missing required parameters in delete_carton_product")
        return jsonify({
            'status': 'error',
            'message': 'orderId, cartonNumber, and productCode are required'
        }), 400
    
    order_id = int(data['orderId'])
    carton_number = int(data['cartonNumber'])
    product_code = data['productCode']
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # First get the quantities that are currently packed in this carton
        cursor.execute("""
            SELECT quantity_packed_pcs, quantity_packed_sets
            FROM packed_products
            WHERE order_id = ? AND carton_number = ? AND product_code = ?
        """, (order_id, carton_number, product_code))
        
        packed_data = cursor.fetchone()
        if not packed_data:
            logger.warning(f"Product {product_code} not found in carton {carton_number} for order {order_id}")
            conn.close()
            return jsonify({
                'status': 'error',
                'message': f"Product {product_code} not found in carton {carton_number}"
            }), 404
        
        quantity_packed_pcs, quantity_packed_sets = packed_data
        
        # Update the order_details table to reduce the packed counts
        cursor.execute("""
            UPDATE order_details
            SET packed_pcs = packed_pcs - ?, packed_sets = packed_sets - ?
            WHERE order_id = ? AND product_code = ?
        """, (quantity_packed_pcs, quantity_packed_sets, order_id, product_code))
        
        # Delete the product from the packed_products table
        cursor.execute("""
            DELETE FROM packed_products
            WHERE order_id = ? AND carton_number = ? AND product_code = ?
        """, (order_id, carton_number, product_code))
        
        conn.commit()
        
        logger.info(f"Removed product {product_code} from carton {carton_number} for order {order_id}")
        return jsonify({
            'status': 'success',
            'message': 'Product removed from carton successfully',
            'orderId': order_id,
            'cartonNumber': carton_number,
            'productCode': product_code,
            'quantityUnpackedPcs': quantity_packed_pcs,
            'quantityUnpackedSets': quantity_packed_sets
        })
        
    except sqlite3.Error as e:
        conn.rollback()
        logger.error(f"Database error in delete_carton_product: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Database error: {str(e)}"
        }), 500
        
    except Exception as e:
        conn.rollback()
        logger.error(f"Unexpected error in delete_carton_product: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Unexpected error: {str(e)}"
        }), 500
        
    finally:
        conn.close()

@app.route('/add_carton_product', methods=['POST'])
def add_carton_product():
    """Add a product to a carton with PC and SET quantities.

    This endpoint adds a product to a specific carton for an order.
    It handles both PC and SET quantities and updates the packed counts accordingly.

    Request body should contain:
    - order_id: The ID of the order
    - carton_number: The carton number to add the product to
    - product_code: The product code to add
    - quantity_pcs: The quantity in PCS to add (optional if quantity_sets is provided)
    - quantity_sets: The quantity in SETS to add (optional if quantity_pcs is provided)

    Returns:
        JSON: Success or error message
    """
    logger.info("API request received: add_carton_product")
    
    data = request.get_json()
    
    # Check for required parameters
    if not data or 'order_id' not in data or 'carton_number' not in data or 'product_code' not in data:
        logger.error("Missing required parameters in add_carton_product")
        return jsonify({
            'status': 'error',
            'message': 'order_id, carton_number, and product_code are required'
        }), 400
    
    # Ensure at least one quantity field is provided
    if ('quantity_pcs' not in data or not data['quantity_pcs']) and \
       ('quantity_sets' not in data or not data['quantity_sets']):
        logger.error("No quantity provided for adding to carton")
        return jsonify({
            'status': 'error',
            'message': 'At least one of quantity_pcs or quantity_sets must be provided'
        }), 400
    
    order_id = int(data['order_id'])
    carton_number = int(data['carton_number'])
    product_code = data['product_code']
    quantity_pcs = int(data.get('quantity_pcs', 0))
    quantity_sets = int(data.get('quantity_sets', 0))
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # First verify the order exists and the product is in the order
        cursor.execute("""
            SELECT od.quantity_pcs, od.quantity_sets, od.packed_pcs, od.packed_sets 
            FROM order_details od
            WHERE od.order_id = ? AND od.product_code = ?
        """, (order_id, product_code))
        
        product_details = cursor.fetchone()
        if not product_details:
            logger.warning(f"Product {product_code} not found in order {order_id}")
            conn.close()
            return jsonify({
                'status': 'error',
                'message': f"Product {product_code} not found in order {order_id}"
            }), 404
        
        quantity_pcs_order, quantity_sets_order, packed_pcs, packed_sets = product_details
        
        # Calculate new packed totals
        new_packed_pcs = packed_pcs + quantity_pcs
        new_packed_sets = packed_sets + quantity_sets
        
        # Check if the requested quantities would exceed the order quantities
        if new_packed_pcs > quantity_pcs_order:
            logger.warning(f"Attempted to pack {new_packed_pcs} PCS for product {product_code}, but order only has {quantity_pcs_order}")
            conn.close()
            return jsonify({
                'status': 'error',
                'message': f"Cannot pack {new_packed_pcs} PCS when order only has {quantity_pcs_order}"
            }), 400
        
        if new_packed_sets > quantity_sets_order:
            logger.warning(f"Attempted to pack {new_packed_sets} SETS for product {product_code}, but order only has {quantity_sets_order}")
            conn.close()
            return jsonify({
                'status': 'error',
                'message': f"Cannot pack {new_packed_sets} SETS when order only has {quantity_sets_order}"
            }), 400
        
        # Check if this product is already in this carton
        cursor.execute("""
            SELECT id, quantity_packed_pcs, quantity_packed_sets
            FROM packed_products
            WHERE order_id = ? AND carton_number = ? AND product_code = ?
        """, (order_id, carton_number, product_code))
        
        existing_pack = cursor.fetchone()
        
        if existing_pack:
            # Update existing record - add to the existing quantities
            cursor.execute("""
                UPDATE packed_products
                SET quantity_packed_pcs = ?, quantity_packed_sets = ?
                WHERE id = ?
            """, (existing_pack[1] + quantity_pcs, 
                 existing_pack[2] + quantity_sets, 
                 existing_pack[0]))
            
            logger.info(f"Updated product {product_code} in carton {carton_number} for order {order_id}")
        else:
            # Insert new record
            cursor.execute("""
                INSERT INTO packed_products
                (order_id, carton_number, product_code, quantity_packed_pcs, quantity_packed_sets)
                VALUES (?, ?, ?, ?, ?)
            """, (order_id, carton_number, product_code, quantity_pcs, quantity_sets))
            
            logger.info(f"Added product {product_code} to carton {carton_number} for order {order_id}")
        
        # Update the order_details table with the new packed totals
        cursor.execute("""
            UPDATE order_details
            SET packed_pcs = ?, packed_sets = ?
            WHERE order_id = ? AND product_code = ?
        """, (new_packed_pcs, new_packed_sets, order_id, product_code))
        
        conn.commit()
        
        return jsonify({
            'status': 'success',
            'message': 'Product added to carton successfully',
            'orderId': order_id,
            'cartonNumber': carton_number,
            'productCode': product_code,
            'quantityPackedPcs': quantity_pcs,
            'quantityPackedSets': quantity_sets,
            'newTotalPackedPcs': new_packed_pcs,
            'newTotalPackedSets': new_packed_sets
        })
        
    except sqlite3.Error as e:
        conn.rollback()
        logger.error(f"Database error in add_carton_product: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Database error: {str(e)}"
        }), 500
        
    except Exception as e:
        conn.rollback()
        logger.error(f"Unexpected error in add_carton_product: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"Unexpected error: {str(e)}"
        }), 500
        
    finally:
        conn.close()

#==============================================================================
#                         MAIN ENTRY POINT
#==============================================================================
# This is where the program starts when run directly

if __name__ == '__main__':
    # Display a welcome message
    logger.info("""
    ╔════════════════════════════════════════════════════════════╗
    ║                                                            ║
    ║             TRIONE BATH CORPORATION ERP SYSTEM             ║
    ║                                                            ║
    ║                      Version 1.4.6                         ║
    ║                                                            ║
    ╚════════════════════════════════════════════════════════════╝
    """)

    # Initialize the database first - this creates all our tables
    # if they don't already exist
    logger.info("Initializing system...")
    init_db()
    logger.info("Database initialized.")

    # Register the manufacturing blueprint
    register_manufacturing_blueprint(app)

    # Register the purchase order blueprint
    register_purchase_order_blueprint(app)

    # Register WhatsApp service blueprint
    register_whatsapp_service_blueprint(app)

    # Integrate the reporting functionality
    integrate_reports(app)
    print("Welp it did start working after all")
    # Start the server - this will run until the program is terminated
    logger.info("All systems go! Starting Trione Bath Corporation ERP server...")
    run_server()