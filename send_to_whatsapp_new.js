const { default: makeWASocket, useMultiFileAuthState, Browsers, DisconnectReason } = require('@whiskeysockets/baileys');
const fs = require('fs');
const path = require('path'); // Import path module
const rimraf = require('rimraf'); // For safely deleting directories

// Define auth folder
const authFolder = 'auth';

// Function to clear auth state when needed
function clearAuthState() {
    console.log('Clearing auth state to fix connection issues');
    if (fs.existsSync(authFolder)) {
        try {
            rimraf.sync(authFolder);
            fs.mkdirSync(authFolder, { recursive: true });
            console.log('Auth state cleared successfully');
        } catch (err) {
            console.error('Failed to clear auth state:', err);
        }
    }
}

// Log to file for better debugging
function logToFile(message) {
    const logPath = 'whatsapp_logs.txt';
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - ${message}\n`;
    
    try {
        fs.appendFileSync(logPath, logEntry);
    } catch (err) {
        console.error('Failed to write to log file:', err);
    }
    
    console.log(message);
}
function logRequestToFile(message){
    const logPath = 'request_logs.txt';
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - ${message}\n`;
    fs.appendFileSync(logPath, logEntry);
}

async function sendWhatsAppMessage(phoneNumber, filePath) {
    logToFile(`Starting WhatsApp send process for ${filePath} to ${phoneNumber}`);
    
    // Create auth folder if it doesn't exist
    if (!fs.existsSync(authFolder)) {
        fs.mkdirSync(authFolder, { recursive: true });
    }

    // Check if file exists before proceeding
    if (!fs.existsSync(filePath)) {
        logToFile(`File not found: ${filePath}`);
        process.exit(5);
    }

    // Extract file name and determine mimetype
    const fileName = path.basename(filePath);
    const ext = path.extname(filePath).toLowerCase();
    let mimetype = 'application/pdf';
    
    if (ext === '.xlsx' || ext === '.xls') {
        mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    }

    // Set a timeout for the entire operation
    const timeout = setTimeout(() => {
        logToFile('Operation timed out after 90 seconds');
        process.exit(2);
    }, 90000); // Extended timeout to 90 seconds

    let retriesLeft = 2; // Allow one retry after clearing auth
    
    // Function for connection attempt with retry logic
    async function attemptConnection() {
        // Load auth state
        const { state, saveCreds } = await useMultiFileAuthState(authFolder);

        logToFile('Creating WhatsApp connection...');
        
        const sock = makeWASocket({
            printQRInTerminal: true,
            auth: state,
            browser: Browsers.ubuntu('Chrome'),
            syncFullHistory: false,
            connectTimeoutMs: 30000,
            keepAliveIntervalMs: 10000,
            retryRequestDelayMs: 2000
        });

        // Save credentials whenever updated
        sock.ev.on('creds.update', saveCreds);
        
        // Listen for message status updates
        sock.ev.on('messages.update', updates => {
            for (const update of updates) {
                if (update.key && update.update.status) {
                    logToFile(`Message status update for ${update.key.id}: ${update.update.status}`);
                }
            }
        });

        return new Promise((resolve, reject) => {
            let hasLoggedQR = false;
            let messageId = null;

            sock.ev.on('connection.update', async (update) => {
                const { connection, lastDisconnect, qr } = update;
                
                if (qr) {
                    if (!hasLoggedQR) {
                        logToFile('QR Code available - Please scan with WhatsApp mobile app');
                        hasLoggedQR = true;
                    }
                }
                
                if (connection === 'close') {
                    const statusCode = lastDisconnect?.error?.output?.statusCode;
                    const reason = lastDisconnect?.error?.output?.payload?.error;
                    
                    logToFile(`Connection closed. Status: ${statusCode}, Reason: ${reason}`);
                    logToFile(`Error details: ${JSON.stringify(lastDisconnect?.error || {})}`);
                    
                    // Check for specific errors that require clearing auth
                    if (
                        statusCode === 428 || // Need to scan QR code
                        statusCode === 401 || // Unauthorized
                        reason === 'session-expired' ||
                        reason === 'kicked' || 
                        retriesLeft > 0 && (
                            lastDisconnect?.error?.message?.includes('closed') ||
                            lastDisconnect?.error?.message?.includes('timed out') ||
                            lastDisconnect?.error?.message?.includes('refused')
                        )
                    ) {
                        if (retriesLeft > 0) {
                            retriesLeft--;
                            logToFile(`Retrying connection (${2-retriesLeft}/2)...`);
                            clearAuthState();
                            
                            // Wait a bit before retrying
                            await new Promise(r => setTimeout(r, 2000));
                            resolve(attemptConnection()); // Recursive retry with cleared auth
                        } else {
                            logToFile('Authentication failed after retries');
                            clearTimeout(timeout);
                            process.exit(3);
                        }
                    } else {
                        logToFile(`Connection closed due to: ${reason || 'Unknown reason'}`);
                        clearTimeout(timeout);
                        process.exit(4);
                    }
                } else if (connection === 'open') {
                    logToFile('Connection established successfully');
                    
                    try {
                        // Ensure we have a valid connection
                        await new Promise(r => setTimeout(r, 1000));
                        
                        logToFile(`Sending file: ${fileName} to ${phoneNumber}`);
                        
                        // Prepare the message
                        const message = {
                            document: { url: filePath },
                            mimetype: mimetype,
                            fileName: fileName,
                            caption: `Here is your order ${fileName}`
                        };
                        
                        // Send the document
                        const sentMsg = await sock.sendMessage(phoneNumber + '@s.whatsapp.net', message);
                        messageId = sentMsg.key.id;
                        
                        logToFile(`Message sent with ID: ${messageId}`);
                        
                        // Wait for a moment to let delivery receipts come in
                        await new Promise(r => setTimeout(r, 5000));
                        
                        logToFile('Message sent successfully');
                        clearTimeout(timeout);
                        process.exit(0);
                    } catch (error) {
                        logToFile(`Failed to send message: ${error.message}`);
                        logToFile(`Error stack: ${error.stack}`);
                        clearTimeout(timeout);
                        process.exit(1);
                    }
                } else if (connection === 'connecting') {
                    logToFile('Connecting to WhatsApp...');
                }
            });
        });
    }

    // Start the connection process with retry logic
    return attemptConnection();
}

// Get arguments from command line
const phoneNumber = process.argv[2];
const filePath = process.argv[3];

if (!phoneNumber || !filePath) {
    console.error('Usage: node send_to_whatsapp.js <phone_number> <file_path>');
    process.exit(1);
}

// Log inputs for debugging
logRequestToFile(`Starting send process`);
logRequestToFile(`Phone Number: ${phoneNumber}`);
logRequestToFile(`File Path: ${filePath}`);

// Handle unhandled rejection errors
process.on('unhandledRejection', (reason, promise) => {
    logRequestToFile(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
    process.exit(1);
});

sendWhatsAppMessage(phoneNumber, filePath);
