import os
import requests
import datetime
import shutil
from variables import UPDATE_TOKEN
import base64
import argparse

# --- CONFIG ---
REPO_OWNER = "Ivorisnoob"
REPO_NAME = "Trione_ERP"
BRANCH = "main"
API_URL = f"https://api.github.com/repos/{REPO_OWNER}/{REPO_NAME}"
HEADERS = {"Authorization": f"token {UPDATE_TOKEN}"}
BACKUP_ROOT_DIR = "update_backups"  # Dedicated backup folder
DB_FILES_TO_ALWAYS_BACKUP = [
    "Trione_ERP_Database.db",
    "manufacturing.db",
    "client_db.db"
]


def get_changed_files_since(date, include_db=False):
    url = f"{API_URL}/commits?sha={BRANCH}&since={date.isoformat()}"
    r = requests.get(url, headers=HEADERS)
    r.raise_for_status()
    commits = r.json()
    changed_files = set()
    for commit in commits:
        commit_url = commit["url"]
        commit_data = requests.get(commit_url, headers=HEADERS).json()
        for file in commit_data.get("files", []):
            filename = file["filename"]
            if not include_db and filename.endswith(".db"):
                continue
            changed_files.add(filename)
    return list(changed_files)


def download_and_replace_file(path):
    # Use GitHub API to get file content (works for private repos)
    api_url = f"{API_URL}/contents/{path}?ref={BRANCH}"
    r = requests.get(api_url, headers=HEADERS)
    if r.status_code == 200:
        data = r.json()
        file_content = data["content"]
        encoding = data.get("encoding", "")

        # Backup existing file by moving it
        if os.path.exists(path):
            try:
                original_file_dir = os.path.dirname(path)
                original_filename = os.path.basename(path)
                filename_base, filename_ext = os.path.splitext(original_filename)
                
                backup_timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
                timestamped_backup_filename = f"{filename_base}_{backup_timestamp}{filename_ext}"
                
                # Construct the target backup directory, preserving subfolder structure
                target_backup_subdir = BACKUP_ROOT_DIR
                if original_file_dir: # If the file is not in the root directory
                    target_backup_subdir = os.path.join(BACKUP_ROOT_DIR, original_file_dir)
                
                os.makedirs(target_backup_subdir, exist_ok=True)
                
                full_backup_path = os.path.join(target_backup_subdir, timestamped_backup_filename)
                
                shutil.move(path, full_backup_path)
                print(f"Moved existing file: {path} to {full_backup_path}")
            except Exception as e:
                print(f"Failed to move existing file {path} for backup: {e}")
                # Optionally, decide if you want to proceed without a backup or stop
                # For now, we'll just print the error and continue, the new file will overwrite

        # Ensure target directory for the new/updated file exists
        dir_name = os.path.dirname(path)
        if dir_name: # Only create if dir_name is not empty (i.e., not root)
            os.makedirs(dir_name, exist_ok=True)
        
        mode = "wb" if encoding == "base64" else "w"
        with open(path, mode) as f:
            if encoding == "base64":
                f.write(base64.b64decode(file_content))
            else:
                f.write(file_content)
        print(f"Updated: {path}")
    else:
        print(f"Failed to download: {path} (status {r.status_code})")


def main():
    parser = argparse.ArgumentParser(description="Update files from GitHub repo.")
    parser.add_argument('--include-db', action='store_true', help='Also update .db files')
    args = parser.parse_args()

    # Ensure backup directory exists
    os.makedirs(BACKUP_ROOT_DIR, exist_ok=True)

    # Perform mandatory backups of specified DB files
    print(f"Performing mandatory backups to {BACKUP_ROOT_DIR}...")
    for db_file_path in DB_FILES_TO_ALWAYS_BACKUP:
        if os.path.exists(db_file_path):
            try:
                db_filename_base, db_filename_ext = os.path.splitext(os.path.basename(db_file_path))
                backup_timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
                timestamped_db_filename = f"{db_filename_base}_{backup_timestamp}{db_filename_ext}"
                db_backup_target_path = os.path.join(BACKUP_ROOT_DIR, timestamped_db_filename)
                
                shutil.copy2(db_file_path, db_backup_target_path)
                print(f"Backed up database: {db_file_path} to {db_backup_target_path}")
            except Exception as e:
                print(f"Failed to backup database {db_file_path}: {e}")
        else:
            print(f"Database file not found, skipping backup: {db_file_path}")

    # Calculate the timestamp 24 hours ago from now
    since = datetime.datetime.utcnow() - datetime.timedelta(days=1)
    print(f"Checking for changed files since: {since.isoformat()}")

    changed_files = get_changed_files_since(since, include_db=args.include_db)
    if not changed_files:
        print("No files changed in the last 24 hours.")
        return

    for file in changed_files:
        if file.startswith("auth/"):
            print(f"Skipping file in auth/: {file}")
            continue
        download_and_replace_file(file)


if __name__ == "__main__":
    main()
