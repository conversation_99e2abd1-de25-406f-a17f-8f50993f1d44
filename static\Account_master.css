:root {
    /* New Color Palette - "Cyber Dark" */
    --background: #0d1117; /* Deep, dark blue-gray */
    --foreground: #c9d1d9; /* Soft white/light gray */
    --card: #161b22;       /* Slightly lighter dark blue-gray */
    --card-foreground: #c9d1d9;
    --popover: #161b22;
    --primary: #58a6ff;    /* Bright blue for primary actions */
    --primary-foreground: #0d1117;
    --secondary: #21262d;  /* Medium dark gray for secondary elements */
    --muted: #21262d;
    --muted-foreground: #8b949e; /* Muted gray for less important text */
    --accent: #30363d;     /* Darker gray for accents */
    --accent-foreground: #c9d1d9;
    --border: #30363d;     /* Subtle border color */
    --radius: 0.6rem;      /* Slightly adjusted radius for a modern feel */

    /* Additional new variables for enhanced styling */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-inset: inset 0 2px 4px 0 rgba(0,0,0,0.06);
    --success: #2ea043;
    --danger: #f85149;
    /* RGB value for primary for use in rgba values with opacity */
    /* Manually update if --primary (#58a6ff) changes. R:88, G:166, B:255 */
    --rgb-primary: 88, 166, 255;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    /* transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease; */ /* Global transition for smoother theme changes - might be too much */
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    background: var(--background);
    color: var(--foreground);
    line-height: 1.6;
    padding: 2rem; /* Increased padding for more breathing room */
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Modern Header Styles */
.header {
    margin-bottom: 3.5rem; /* Increased margin */
    padding: 2.5rem;     /* Increased padding */
    border-radius: var(--radius);
    background: var(--card);
    border: 1px solid var(--border);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px; /* Slightly thicker line */
    background: linear-gradient(
        90deg,
        transparent,
        var(--primary),
        transparent
    );
    opacity: 0.6;
}

.header h1 {
    font-size: 2.8rem; /* Slightly larger */
    font-weight: 700;
    margin-bottom: 2rem; /* Increased margin */
    background: linear-gradient(to right, var(--primary), #79c0ff); /* Adjusted gradient */
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-align: center;
}

/* Enhanced Search Bar */
.search-container {
    max-width: 700px; /* Wider search bar */
    margin: 0 auto;
    position: relative;
}

.search-bar {
    width: 100%;
    padding: 1.1rem 1.75rem; /* Increased padding */
    font-size: 1.05rem;    /* Slightly larger font */
    background: var(--secondary);
    border: 1px solid var(--border);
    border-radius: calc(var(--radius) + 0.2rem); /* Slightly more rounded */
    color: var(--foreground);
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1); /* Smoother transition */
    outline: none;
    box-shadow: var(--shadow-inset);
}

.search-bar:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(var(--rgb-primary, 88, 166, 255), 0.25), var(--shadow-inset); /* RGB value for primary for opacity */
    background-color: var(--background); /* Change background on focus */
}

.search-bar::placeholder {
    color: var(--muted-foreground);
    opacity: 0.8;
}


/* Modern Card Grid */
.client-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(360px, 1fr)); /* Slightly wider cards */
    gap: 2rem; /* Increased gap */
    max-width: 1600px; /* Wider max width */
    margin: 0 auto;
}

/* Enhanced Client Cards */
.client-card {
    background: var(--card);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    overflow: hidden;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.3s ease;
    position: relative;
    box-shadow: var(--shadow-sm);
}

.client-card:hover {
    transform: translateY(-5px) scale(1.01); /* More pronounced hover effect */
    border-color: var(--primary);
    box-shadow: var(--shadow-lg), 0 0 15px rgba(var(--rgb-primary, 88, 166, 255), 0.1); /* Enhanced shadow */
}

.client-card::before { /* Removed the top border animation for a cleaner look, can be re-added if desired */
    content: none;
}


.card-content {
    padding: 2rem; /* Increased padding */
}

.section {
    margin-bottom: 2rem; /* Increased margin */
    animation: fadeIn 0.6s ease-out forwards; /* Ensure animation stays at end state */
    opacity: 0; /* Start hidden for animation */
}

.section:not(:last-child) {
    border-bottom: 1px solid var(--accent); /* Subtle separator */
    padding-bottom: 1.5rem;
}


.section-title {
    font-size: 1.25rem; /* Larger title */
    font-weight: 600;
    color: var(--primary);
    margin-bottom: 1.25rem; /* Increased margin */
    padding-bottom: 0.75rem; /* Increased padding */
    /* border-bottom: 1px solid var(--border); Removed, section has border now */
    display: flex;
    align-items: center;
}

.section-title::before { /* Decorative element for section titles */
    content: '';
    display: inline-block;
    width: 4px;
    height: 1.25rem; /* Match font size */
    background-color: var(--primary);
    margin-right: 0.75rem;
    border-radius: 2px;
    opacity: 0.8;
}


/* Modern Badge Design */
.badge {
    display: inline-flex;
    align-items: center;
    background: rgba(var(--rgb-primary, 88, 166, 255), 0.1); /* Use primary with alpha */
    color: var(--primary);
    padding: 0.5rem 1.2rem; /* Increased padding */
    border-radius: calc(var(--radius) / 1.5); /* Adjusted radius */
    font-size: 0.9rem; /* Slightly larger font */
    font-weight: 500;
    margin-bottom: 1.25rem; /* Increased margin */
    border: 1px solid transparent; /* Initially transparent border */
    transition: all 0.3s ease;
}

.badge:hover {
    background: rgba(var(--rgb-primary, 88, 166, 255), 0.2);
    border-color: rgba(var(--rgb-primary, 88, 166, 255), 0.3);
}


/* Enhanced Copy Button */
.copy-btn {
    background: var(--secondary);
    color: var(--muted-foreground);
    border: 1px solid var(--border);
    border-radius: var(--radius); /* Consistent radius */
    padding: 0.5rem 1rem; /* Increased padding */
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 0.75rem; /* Increased margin */
    outline: none;
}

.copy-btn:hover {
    background: var(--primary);
    color: var(--primary-foreground);
    border-color: var(--primary);
    box-shadow: 0 0 8px rgba(var(--rgb-primary, 88, 166, 255), 0.3);
}

.copy-btn:active {
    transform: scale(0.95);
}


/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(15px); /* Slightly more pronounced */
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Removed shimmer, not used */

.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2.5rem; /* Increased padding */
    background: transparent; /* Make it blend better */
    border-radius: var(--radius);
    /* border: 1px solid var(--border); */ /* Removed border for cleaner look */
    width: 100%;
    grid-column: 1 / -1; /* Span all columns if in grid */
    color: var(--muted-foreground);
    font-size: 1.1rem;
}

.spinner {
    width: 48px; /* Larger spinner */
    height: 48px;
    border: 4px solid var(--accent); /* Thicker border */
    border-top-color: var(--primary);
    border-radius: 50%;
    animation: spin 0.8s linear infinite; /* Faster spin */
    margin-right: 1rem;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.client-info {
    color: var(--muted-foreground);
    margin-bottom: 0.75rem; /* Increased margin */
    font-size: 0.95rem;
    line-height: 1.7;
}

.client-info strong {
    color: var(--foreground);
    font-weight: 500;
}

.notes {
    background: var(--secondary);
    border-left: 4px solid var(--primary); /* Thicker accent border */
    padding: 1.25rem; /* Increased padding */
    border-radius: var(--radius); /* Consistent radius */
    margin-top: 1.25rem; /* Increased margin */
    box-shadow: var(--shadow-sm);
}

.notes p:not(:last-child) {
    margin-bottom: 0.5rem;
}

/* No Results Message */
.no-results {
    text-align: center;
    padding: 3rem 1rem;
    font-size: 1.1rem;
    color: var(--muted-foreground);
    background-color: var(--card);
    border-radius: var(--radius);
    border: 1px dashed var(--border);
    margin-top: 2rem;
    box-shadow: var(--shadow-sm);
}

/* Toast Messages for Copy Feedback */
.copy-success, .copy-error {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%); /* Base centered position */
    padding: 1rem 1.5rem;
    border-radius: var(--radius);
    color: var(--primary-foreground);
    font-weight: 500;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    opacity: 0; /* Start transparent */
    animation: toastInRight 0.5s ease-out forwards;
}

.copy-success {
    background-color: var(--success); /* Green for success */
}

.copy-error {
    background-color: var(--danger); /* Red for error */
}

@keyframes toastInRight {
    from {
        transform: translateX(calc(-50% + 50vw)); /* Start from right of viewport, accounting for initial -50% shift */
        opacity: 0;
    }
    to {
        transform: translateX(-50%); /* Final centered position */
        opacity: 1;
    }
}


/* Responsive Adjustments */
@media (max-width: 768px) {
    body {
        padding: 1rem;
    }
    .header {
        padding: 1.5rem;
        margin-bottom: 2.5rem;
    }

    .header h1 {
        font-size: 2.2rem;
    }

    .search-bar {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .client-list {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .card-content {
        padding: 1.5rem;
    }

    .section-title {
        font-size: 1.15rem;
    }
}

/* Add a definition for --rgb-primary for use in rgba values with opacity */
/* This needs to be manually updated if --primary changes */
/* Ideally, this would be handled by a CSS preprocessor or JS */
/* For now, let's assume --primary: #58a6ff; -> R:88, G:166, B:255 */
/* :root {  -- MOVED THIS TO THE TOP --
    --rgb-primary: 88, 166, 255;
     ... other root variables ... 
} */
