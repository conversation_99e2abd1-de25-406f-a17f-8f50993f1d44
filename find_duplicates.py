import re
from collections import defaultdict

ROUTE_PATTERN = re.compile(r"@app\.route\(([^)]*)\)")
DEF_PATTERN = re.compile(r"def ([a-zA-Z_][a-zA-Z0-9_]*)\s*\(")
METHODS_PATTERN = re.compile(r"methods\s*=\s*\[([^\]]*)\]")

filename = "Trione_Backend.py"

route_map = defaultdict(list)  # (route, methods) -> [line_numbers]
func_map = defaultdict(list)   # func_name -> [line_numbers]

with open(filename, encoding="utf-8") as f:
    lines = f.readlines()

for i, line in enumerate(lines):
    route_match = ROUTE_PATTERN.search(line)
    if route_match:
        route_args = route_match.group(1)
        # Extract route path (first argument, possibly with quotes)
        path_match = re.match(r"[\'\"]([^\'\"]+)[\'\"]", route_args.strip())
        route_path = path_match.group(1) if path_match else route_args.strip()
        # Extract methods if present
        methods_match = METHODS_PATTERN.search(route_args)
        if methods_match:
            methods = [m.strip().strip("'\"") for m in methods_match.group(1).split(",")]
            methods_key = tuple(sorted(methods))
        else:
            methods_key = ("GET",)  # Default method
        route_map[(route_path, methods_key)].append(i+1)
    # Function definition
    def_match = DEF_PATTERN.match(line)
    if def_match:
        func_name = def_match.group(1)
        func_map[func_name].append(i+1)

# Find duplicate routes
print("Duplicate Flask routes:")
dup_found = False
for (route, methods), line_nums in route_map.items():
    if len(line_nums) > 1:
        dup_found = True
        print(f"  Route: {route}  Methods: {methods}  Lines: {line_nums}")
if not dup_found:
    print("  None found.")

# Find duplicate function names
print("\nDuplicate function names:")
dup_found = False
for func, line_nums in func_map.items():
    if len(line_nums) > 1:
        dup_found = True
        print(f"  Function: {func}  Lines: {line_nums}")
if not dup_found:
    print("  None found.") 