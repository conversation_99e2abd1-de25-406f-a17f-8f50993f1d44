# Make sure 'db' is imported (e.g., import db_schema as db)
import db_schema as db # Assuming the module is db_schema.py
from flask import Blueprint, render_template, request, jsonify, abort, redirect, url_for, send_from_directory, current_app, Response
from datetime import datetime, date
import json
import os
import time
import sqlite3
from werkzeug.utils import secure_filename
import base64
import logging
# Create a Blueprint for manufacturing routes
manufacturing = Blueprint('manufacturing', __name__, url_prefix='/manufacturing')

# Get a logger instance
logger = logging.getLogger(__name__)

# Run schema updates when the blueprint is registered
@manufacturing.before_app_request
def update_schemas():
    """Update database schemas to ensure compatibility."""
    # Use a flag to ensure this runs only once
    if not hasattr(update_schemas, 'has_run'):
        print("Updating database schemas...")
        db.update_final_store_transaction_schema()
        print("Database schemas updated successfully.")
        update_schemas.has_run = True

# Route for the all products page
@manufacturing.route('/all_products')
def serve_all_products_page():
    """Serve the all products page that shows all products with their brand information."""
    return render_template('manufacturing/all_products.html')

# Define upload folder and allowed extensions (Consider moving to Flask app config)
UPLOAD_FOLDER = 'uploads/products'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Ensure the upload directory exists when the blueprint is registered or app starts
# This might be better placed in the main app setup
try:
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)
    # Also create the main uploads directory if it doesn't exist
    if not os.path.exists('uploads'):
         os.makedirs('uploads')
except OSError as e:
    print(f"Error creating upload directory {UPLOAD_FOLDER}: {e}")

# Helper function to save product photo
def save_product_photo(file, product_code):
    if file and allowed_file(file.filename):
        # Generate a more unique filename to avoid collisions and keep it somewhat readable
        # Example: PRODUCTCODE_timestamp.ext or PRODUCTCODE_uuid.ext
        # Using secure_filename is crucial to prevent directory traversal etc.
        base, ext = os.path.splitext(file.filename)
        # Simple unique name: product_code + timestamp
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f")
        filename = secure_filename(f"{product_code}_{timestamp}{ext}")

        save_path = os.path.join(UPLOAD_FOLDER, filename)
        try:
            file.save(save_path)
            print(f"Saved photo to: {save_path}")
            # Return the relative path from the application root for storage in DB
            # We need a way to serve this later, so store path relative to UPLOAD_FOLDER maybe?
            # Or store just the filename and construct path when needed. Let's store just filename.
            return filename
        except Exception as e:
            print(f"Error saving file {filename}: {e}")
            return None
    elif file: # File provided but not allowed extension
        print(f"File extension not allowed: {file.filename}")
        # Consider raising a specific error or returning a special value
        raise ValueError(f"Invalid file type. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}")
    return None # No file provided or error occurred before saving

# Helper function to delete product photo
def delete_product_photo(filename):
    if not filename:
        return False
    # If it's a base64 string, there's no separate file to delete.
    # The data is in the database and will be removed when the product record is deleted.
    if filename.startswith('base64:'):
        print(f"Photo for {filename[:20]}... is stored as base64, no file to delete.")
        return True
    try:
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"Deleted photo: {file_path}")
            return True
        else:
             print(f"Photo not found for deletion: {file_path}")
             return False # File didn't exist
    except Exception as e:
        print(f"Error deleting photo {filename}: {e}")
        return False

# Helper function to convert SQLite Row objects to dictionaries
def dict_factory(row):
    if isinstance(row, dict):
        return row  # Already a dict
    if hasattr(row, 'keys'): # Check if it's a Row object or similar mapping
        try:
            return dict(row)  # Standard conversion for sqlite3.Row
        except Exception as e:
            # Log the error and the type of row for debugging
            logger.error(f"Error converting row to dict: {e}. Row type: {type(row)}, Row keys (if any): {getattr(row, 'keys', lambda: 'N/A')()}")
            # Fallback to a more manual conversion if dict(row) fails unexpectedly
            return {key: row[key] for key in row.keys()}
    logger.warning(f"dict_factory received an unexpected row type: {type(row)}")
    return {} # Fallback for completely unexpected types

# Helper function to parse date strings
def parse_date(date_str):
    if not date_str:
        return None
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        abort(400, description="Invalid date format. Use YYYY-MM-DD")

#-------------------------------------------------------
# Scrap Entry Routes
#-------------------------------------------------------

@manufacturing.route('/scrap_entry')
def serve_scrap_entry_page():
    """Hark! Behold the sacred chamber where we record our manufacturing sacrifices!

    What solemn place is this? 'Tis the hallowed page where we document the
    materials that have been lost in our noble quest to create bath fixtures of
    unparalleled quality! Like a royal scribe recording the casualties of battle,
    this page alloweth our production managers to maintain accurate records of
    the brass and zinc that hath been consumed but not transformed into finished
    products.

    Upon this page, one may enter the particulars of each scrap entry - the date,
    the type of material, the quantity in kilograms, the supplier from whence it
    came, and the cost per kilogram if known. These details are most precious for
    our accounting and efficiency calculations, allowing us to track the true cost
    of our manufacturing operations.

    Though it pains us to acknowledge the existence of scrap, for it representeth
    materials purchased but not transformed into saleable goods, we must face this
    reality with courage and diligence. By accurately tracking our losses, we gain
    the wisdom to improve our processes and reduce waste in the future.

    Let all who bear witness to manufacturing imperfections come forth and record
    them here, that our records might be complete and our analysis accurate!
    """
    return render_template('manufacturing/scrap_entry.html')

@manufacturing.route('/api/scrap_entries', methods=['GET'])
def get_scrap_entries():
    """Forsooth! 'Tis the magical looking glass that revealeth our manufacturing losses!

    What manner of digital sorcery is this? 'Tis a most wondrous endpoint that doth
    summon forth the records of our scrap entries from the depths of our database!
    Like a royal historian recounting the casualties of past battles, this function
    fetcheth the details of materials that have been sacrificed in our quest for
    manufacturing excellence.

    When thou dost dispatch thy GET request to this mystical portal, thou mayest
    specify a limit to the number of entries thou wishest to receive. If no limit
    be specified, the function shall, in its wisdom, provide thee with ten entries,
    for that is a reasonable number to display upon a page without overwhelming the
    viewer's senses.

    The function shall then transform these records from their raw database form
    into the sacred JSON format, that modern wizardry that alloweth information to
    travel betwixt the realms of server and client. Each entry shall be displayed
    with its full panoply of details - the date, the material type, the quantity,
    the supplier, and the cost.

    By my troth, this endpoint is as essential to our scrap tracking system as a
    telescope is to an astronomer! Without it, how would we view our historical
    scrap data? How would we analyze patterns of material loss? 'Twould be as
    trying to navigate by the stars with one's eyes closed!

    So send forth thy GET requests, curious user, and marvel at the scrap entry
    records that shall be returned unto thee!
    """
    limit = request.args.get('limit', 10, type=int)
    entries = db.get_recent_scrap_entries(limit)
    return jsonify([dict_factory(entry) for entry in entries])

@manufacturing.route('/api/scrap_entries', methods=['POST'])
def add_scrap_entry_api():
    """Zounds! 'Tis the mystical forge where new scrap records are born!

    What powerful enchantment is this? 'Tis a most solemn endpoint that doth
    receive the particulars of new scrap entries and commit them to our grand
    archives! Like a royal scribe recording the losses of a recent battle, this
    function taketh the details of materials that have been sacrificed in our
    manufacturing process and enshrines them in our database for posterity.

    When thou dost dispatch thy POST request to this hallowed gateway, bearing
    the particulars of a scrap entry, the function shall, with great scrutiny,
    examine thy submission for completeness. The entry date must be provided,
    for when did this loss occur? The material type must be specified, for what
    manner of material was lost? The quantity must be declared, for how much was
    sacrificed? And the supplier must be named, for whence came this material
    before it met its unfortunate fate?

    Should thy request be found wanting in any of these particulars, the function
    shall reject it with a stern 400 error, proclaiming the nature of the deficiency
    that thou mightest correct it. Furthermore, the material type must be either
    'Brass' or 'Zinc', for these are the only materials we track in our scrap system.
    Any other material shall be rejected as if 'twere a counterfeit coin offered to
    a shrewd merchant.

    But if all is in order, the function shall perform its mystical inscription,
    adding the new scrap entry to our database and returning a message of success
    along with the unique identifier assigned to this entry.

    Odds bodkins! This endpoint is as vital to our scrap tracking system as the
    royal mint is to the kingdom's currency! Without it, how would new scrap entries
    be recorded? How would we maintain accurate records of our material losses?
    'Twould be as trying to keep accounts without a ledger!

    So send forth thy POST requests, diligent user, and rest assured that thy
    scrap records shall be faithfully preserved in our database!
    """
    data = request.json

    if not all(k in data for k in ['entry_date', 'material_type', 'quantity_kg', 'supplier_name']):
        abort(400, description="Missing required fields")

    entry_date = parse_date(data['entry_date'])
    material_type = data['material_type']
    quantity_kg = float(data['quantity_kg'])
    supplier_name = data['supplier_name']
    cost_per_kg = data.get('cost_per_kg')

    if cost_per_kg:
        cost_per_kg = float(cost_per_kg)

    if material_type not in ['Brass', 'Zinc']:
        abort(400, description="Material type must be either 'Brass' or 'Zinc'")

    entry_id = db.add_scrap_entry(entry_date, material_type, quantity_kg, supplier_name, cost_per_kg)

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'Scrap entry added successfully'
    }), 201

#-------------------------------------------------------
# Casting Order Routes
#-------------------------------------------------------

@manufacturing.route('/casting_order')
def serve_casting_order_page():
    """Serve the casting order page."""
    return render_template('manufacturing/casting_order.html')

@manufacturing.route('/api/casting_order', methods=['GET'])
def get_casting_orders():
    """API endpoint to retrieve casting orders."""
    limit = request.args.get('limit', 100, type=int)
    entries = db.get_casting_orders(limit)
    return jsonify([dict_factory(entry) for entry in entries])

@manufacturing.route('/api/casting_order', methods=['POST'])
def add_casting_order_api():
    """API endpoint to add a new casting order."""
    data = request.json

    if not all(k in data for k in ['order_date', 'product_code', 'destination']) or not (data.get('quantity_pcs') or data.get('quantity_sets')):
        abort(400, description="Missing required fields (order_date, product_code, destination, and quantity_pcs or quantity_sets)")

    order_date = parse_date(data['order_date'])
    order_no = data.get('order_no')
    product_code = data['product_code']
    destination = data['destination']
    quantity_pcs = data.get('quantity_pcs')
    quantity_sets = data.get('quantity_sets')
    per_piece_weight = data.get('per_piece_weight')

    if quantity_pcs:
        quantity_pcs = int(quantity_pcs)
    if quantity_sets:
        quantity_sets = int(quantity_sets)
    if per_piece_weight:
        per_piece_weight = float(per_piece_weight)

    if destination not in ['CNC', 'Vraj']:
        abort(400, description="Destination must be either 'CNC' or 'Vraj'")

    entry_id = db.add_casting_order(
        order_date, order_no, product_code, destination, quantity_pcs, quantity_sets, per_piece_weight
    )

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'Casting order added successfully'
    }), 201

#-------------------------------------------------------
# CNC Outward Routes
#-------------------------------------------------------

@manufacturing.route('/cnc_outward')
def serve_cnc_outward_page():
    """Serve the CNC outward page."""
    return render_template('manufacturing/cnc_outward.html')

@manufacturing.route('/api/cnc_outward', methods=['GET'])
def get_cnc_outward_transactions():
    """API endpoint to retrieve CNC outward transactions."""
    limit = request.args.get('limit', 100, type=int)
    entries = db.get_cnc_outward_transactions(limit)
    return jsonify([dict_factory(entry) for entry in entries])

@manufacturing.route('/api/cnc_outward', methods=['POST'])
def add_cnc_outward_api():
    """API endpoint to add a new CNC outward transaction."""
    data = request.json

    if not all(k in data for k in ['outward_date', 'product_code', 'quantity_pcs', 'per_piece_weight']):
        abort(400, description="Missing required fields (outward_date, product_code, quantity_pcs, per_piece_weight)")

    outward_date = parse_date(data['outward_date'])
    product_code = data['product_code']
    quantity_pcs = int(data['quantity_pcs'])
    per_piece_weight = float(data['per_piece_weight'])
    casting_order_id = data.get('casting_order_id')

    entry_id = db.add_cnc_outward(
        outward_date, product_code, quantity_pcs, per_piece_weight, casting_order_id
    )

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'CNC outward transaction added successfully'
    }), 201

#-------------------------------------------------------
# Primary Store Receipt Routes
#-------------------------------------------------------

@manufacturing.route('/primary_store_receipt')
def serve_primary_store_receipt_page():
    """Serve the primary store receipt page."""
    return render_template('manufacturing/primary_store_receipt.html')

@manufacturing.route('/api/primary_store_receipt', methods=['GET'])
def get_primary_store_receipts():
    """API endpoint to retrieve primary store receipts."""
    limit = request.args.get('limit', 100, type=int)
    entries = db.get_primary_store_receipts(limit)
    return jsonify([dict_factory(entry) for entry in entries])

@manufacturing.route('/api/primary_store_receipt', methods=['POST'])
def add_primary_store_receipt_api():
    """API endpoint to add a new primary store receipt."""
    data = request.json

    if not all(k in data for k in ['receipt_date', 'product_code', 'quantity_pcs', 'per_piece_weight']):
        abort(400, description="Missing required fields (receipt_date, product_code, quantity_pcs, per_piece_weight)")

    try:
        receipt_date = parse_date(data['receipt_date'])
        product_code = data['product_code']
        quantity_pcs = int(data['quantity_pcs'])
        per_piece_weight = float(data['per_piece_weight'])
        cnc_outward_id = data.get('cnc_outward_id')

        # Add retry logic for database locked errors
        max_retries = 3
        retry_count = 0
        entry_id = None

        while retry_count < max_retries:
            try:
                entry_id = db.add_primary_store_receipt(
                    receipt_date, product_code, quantity_pcs, per_piece_weight, cnc_outward_id
                )
                break  # Success, exit the loop
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and retry_count < max_retries - 1:
                    retry_count += 1
                    print(f"Database locked, retrying ({retry_count}/{max_retries})...")
                    time.sleep(1)  # Wait before retrying
                else:
                    raise  # Re-raise if max retries reached or different error
    except sqlite3.IntegrityError as e:
        print(f"Database integrity error: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Database integrity error: {str(e)}'
        }), 400
    except sqlite3.OperationalError as e:
        print(f"Database operational error: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Database operational error: {str(e)}'
        }), 503  # Service Unavailable
    except Exception as e:
        print(f"Unexpected error: {e}")
        return jsonify({
            'status': 'error',
            'message': f'An unexpected error occurred: {str(e)}'
        }), 500

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'Primary store receipt added successfully'
    }), 201

#-------------------------------------------------------
# Buffing Outward Routes
#-------------------------------------------------------

@manufacturing.route('/buffing_outward')
def serve_buffing_outward_page():
    """Serve the buffing outward page."""
    return render_template('manufacturing/buffing_outward.html')

@manufacturing.route('/buffing')
def serve_buffing_page():
    """Serve the buffing page."""
    return redirect(url_for('manufacturing.serve_buffing_outward_page'))

@manufacturing.route('/buffing_contractors')
def serve_buffing_contractors_page():
    """Serve the buffing contractors management page."""
    return render_template('manufacturing/buffing_contractors.html')

@manufacturing.route('/api/buffing_outward', methods=['GET'])
def get_buffing_outward_transactions():
    """API endpoint to retrieve buffing outward transactions."""
    limit = request.args.get('limit', 100, type=int)
    entries = db.get_buffing_outward_transactions(limit)
    return jsonify([dict_factory(entry) for entry in entries])

@manufacturing.route('/api/buffing_outward', methods=['POST'])
def add_buffing_outward_api():
    """API endpoint to add a new buffing outward transaction."""
    try:
        print(f"DEBUG: POST /api/buffing_outward received")
        data = request.json
        print(f"DEBUG: Request data: {data}")

        if not all(k in data for k in ['outward_date', 'product_code', 'quantity_pcs', 'per_piece_weight']):
            print(f"DEBUG: Missing required fields in request data")
            abort(400, description="Missing required fields (outward_date, product_code, quantity_pcs, per_piece_weight)")

        outward_date = parse_date(data['outward_date'])
        product_code = data['product_code']
        quantity_pcs = int(data['quantity_pcs'])
        per_piece_weight = float(data['per_piece_weight'])
        primary_store_receipt_id = data.get('primary_store_receipt_id')
        contractor_id = data.get('contractor_id')

        print(f"DEBUG: Parsed data: outward_date={outward_date}, product_code={product_code}, quantity_pcs={quantity_pcs}, per_piece_weight={per_piece_weight}, primary_store_receipt_id={primary_store_receipt_id}, contractor_id={contractor_id}")

        entry_id = db.add_buffing_outward(
            outward_date, product_code, quantity_pcs, per_piece_weight, primary_store_receipt_id, contractor_id
        )

        print(f"DEBUG: Entry added with ID: {entry_id}")

        return jsonify({
            'status': 'success',
            'entry_id': entry_id,
            'message': 'Buffing outward transaction added successfully'
        }), 201
    except Exception as e:
        print(f"DEBUG: Error in add_buffing_outward_api: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'status': 'error',
            'message': f'An error occurred: {str(e)}'
        }), 500

#-------------------------------------------------------
# Buffing Contractor Routes
#-------------------------------------------------------

@manufacturing.route('/api/buffing_contractors', methods=['GET'])
def get_buffing_contractors_api():
    """API endpoint to retrieve buffing contractors."""
    active_only = request.args.get('active_only', 'true').lower() == 'true'
    contractors = db.get_buffing_contractors(active_only)
    return jsonify([dict_factory(contractor) for contractor in contractors])

@manufacturing.route('/api/buffing_contractors', methods=['POST'])
def add_buffing_contractor_api():
    """API endpoint to add a new buffing contractor."""
    data = request.json

    if 'name' not in data or not data['name'].strip():
        abort(400, description="Contractor name is required")

    name = data['name'].strip()
    contact_number = data.get('contact_number')
    address = data.get('address')
    notes = data.get('notes')

    contractor_id = db.add_buffing_contractor(name, contact_number, address, notes)

    if contractor_id is None:
        abort(400, description="Contractor with this name already exists")

    return jsonify({
        'status': 'success',
        'contractor_id': contractor_id,
        'message': 'Buffing contractor added successfully'
    }), 201

@manufacturing.route('/api/buffing_contractors/<int:contractor_id>', methods=['PUT'])
def update_buffing_contractor_api(contractor_id):
    """API endpoint to update an existing buffing contractor."""
    data = request.json

    # At least one field must be provided for update
    if not any(k in data for k in ['name', 'contact_number', 'address', 'notes', 'is_active']):
        abort(400, description="No fields provided for update")

    name = data.get('name')
    contact_number = data.get('contact_number')
    address = data.get('address')
    notes = data.get('notes')
    is_active = data.get('is_active')

    success = db.update_buffing_contractor(contractor_id, name, contact_number, address, notes, is_active)

    if not success:
        abort(404, description="Contractor not found or update failed")

    return jsonify({
        'status': 'success',
        'message': 'Buffing contractor updated successfully'
    })

@manufacturing.route('/api/buffing_contractors/<int:contractor_id>', methods=['DELETE'])
def delete_buffing_contractor_api(contractor_id):
    """API endpoint to delete (soft delete) a buffing contractor."""
    success = db.delete_buffing_contractor(contractor_id)

    if not success:
        abort(404, description="Contractor not found or delete failed")

    return jsonify({
        'status': 'success',
        'message': 'Buffing contractor deleted successfully'
    })

#-------------------------------------------------------
# Final Store Routes
#-------------------------------------------------------

@manufacturing.route('/final_store')
def serve_final_store_page():
    """Serve the final store transaction page."""
    return render_template('manufacturing/final_store_transaction.html')

@manufacturing.route('/api/final_store', methods=['GET'])
def get_final_store_transactions():
    """API endpoint to retrieve final store transactions (IN/OUT)."""
    try:
        limit = request.args.get('limit', 100, type=int)
        current_app.logger.debug(f"Accessed /api/final_store. Fetching final store transactions with limit {limit}")
        
        entries = db.get_final_store_transactions(limit)
        current_app.logger.debug(f"Retrieved {len(entries)} final store transactions from db.get_final_store_transactions")
        
        return jsonify(entries)
    except Exception as e:
        current_app.logger.error(f"ERROR in /api/final_store endpoint: {str(e)}", exc_info=True) # exc_info=True will log the traceback
        return jsonify({"error": str(e)}), 500

@manufacturing.route('/api/final_store/in', methods=['POST'])
def add_final_store_in_api():
    """API endpoint to add an IN transaction to the final store."""
    data = request.json

    if not all(k in data for k in ['transaction_date', 'product_code', 'quantity_pcs', 'per_piece_weight', 'source']):
        abort(400, description="Missing required fields (transaction_date, product_code, quantity_pcs, per_piece_weight, source)")

    transaction_date = parse_date(data['transaction_date'])
    product_code = data['product_code']
    quantity_pcs = int(data['quantity_pcs'])
    per_piece_weight = float(data['per_piece_weight'])
    source = data['source']
    buffing_outward_id = data.get('buffing_outward_id')
    vraj_casting_order_id = data.get('vraj_casting_order_id')
    handle_type = data.get('handle_type', 'Common')

    if source not in ['Buffing', 'Vraj', 'shanenterprise', 'cabtech', 'Rajenterprise','Stock Correction']:
        abort(400, description="Source must be one of: 'Buffing', 'Vraj', 'shanenterprise', 'cabtech', 'Rajenterprise'")

    if handle_type not in ['Common', 'Right side', 'Left side']:
        abort(400, description="Handle type must be one of: 'Common', 'Right side', 'Left side'")

    if source == 'Buffing' and not buffing_outward_id:
        print("Warning: Final store IN from Buffing should ideally link buffing_outward_id")
    if source == 'Vraj' and not vraj_casting_order_id:
        print("Warning: Final store IN from Vraj should ideally link vraj_casting_order_id")

    # For Vraj source, we'll just log the vraj_casting_order_id for reference
    # The actual handling is done in add_final_store_transaction_in
    if source == 'Vraj' and vraj_casting_order_id is not None:
        print(f"Note: Received vraj_casting_order_id: {vraj_casting_order_id} - will be set to NULL in database function")

    # Add retry logic for database locked errors
    max_retries = 3
    retry_count = 0
    entry_id = None
    last_error = None

    while retry_count < max_retries:
        try:
            entry_id = db.add_final_store_transaction_in(
                transaction_date, product_code, quantity_pcs, per_piece_weight, source, 
                buffing_outward_id, vraj_casting_order_id, handle_type
            )
            break  # Success, exit the loop
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                retry_count += 1
                print(f"Database locked, retrying ({retry_count}/{max_retries})...")
                time.sleep(1)  # Wait a bit before retrying
                last_error = e
            else:
                # Re-raise if it's a different operational error
                raise
        except sqlite3.IntegrityError as e:
            # Handle any integrity error
            print(f"Database integrity error: {e}")
            return jsonify({
                'status': 'error',
                'message': f'Database integrity error: {str(e)}'
            }), 400
        except Exception as e:
            # Handle any other exceptions
            print(f"Error in add_final_store_in_api: {e}")
            return jsonify({
                'status': 'error',
                'message': f'An error occurred: {str(e)}'
            }), 500

    # If we've exhausted retries and still have an error
    if entry_id is None and last_error is not None:
        return jsonify({
            'status': 'error',
            'message': f'Database is locked after {max_retries} retries. Please try again later.'
        }), 503  # Service Unavailable

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'Final store IN transaction added successfully'
    }), 201

@manufacturing.route('/api/final_store/out', methods=['POST'])
def add_final_store_out_api():
    """API endpoint to add an OUT transaction from the final store."""
    data = request.json

    if not all(k in data for k in ['transaction_date', 'product_code', 'quantity_pcs', 'per_piece_weight', 'destination']):
        abort(400, description="Missing required fields (transaction_date, product_code, quantity_pcs, per_piece_weight, destination)")

    try:
        transaction_date = parse_date(data['transaction_date'])
        product_code = str(data['product_code']).strip()  # Ensure product_code is a stripped string
        quantity_pcs = int(data['quantity_pcs'])
        per_piece_weight = float(data['per_piece_weight'])
        destination = data['destination']
        order_id = data.get('order_id')
        handle_type = data.get('handle_type', 'Common')

        # Log incoming request details
        print(f"DEBUG: Final store OUT request - product_code: {product_code}, quantity: {quantity_pcs}, destination: {destination}")

        if destination not in ['Plating', 'Dispatch','Stock Correction']:
            abort(400, description="Destination must be either 'Plating' or 'Dispatch'")

        if handle_type not in ['Common', 'Right side', 'Left side']:
            abort(400, description="Handle type must be one of: 'Common', 'Right side', 'Left side'")

        # Check for inventory before allowing the transaction
        conn = db.get_db_connection()
        cursor = conn.cursor()
        
        # Query final store inventory for this product and handle type
        cursor.execute('''
        SELECT 
            COALESCE(SUM(CASE WHEN transaction_type = 'IN' THEN quantity_pcs ELSE 0 END), 0) -
            COALESCE(SUM(CASE WHEN transaction_type = 'OUT' THEN quantity_pcs ELSE 0 END), 0) as available_stock
        FROM final_store_transaction 
        WHERE product_code = ? AND handle_type = ?
        ''', (product_code, handle_type))
        
        result = cursor.fetchone()
        available_stock = result['available_stock'] if result else 0
        
        # For debugging, check all transactions for this product
        cursor.execute('''
        SELECT transaction_type, quantity_pcs, transaction_date
        FROM final_store_transaction 
        WHERE product_code = ?
        ORDER BY transaction_date DESC
        ''', (product_code,))
        
        transactions = cursor.fetchall()
        print(f"DEBUG: Transactions for {product_code}: {len(transactions)} records")
        for t in transactions:
            print(f"DEBUG: {t['transaction_type']} - {t['quantity_pcs']} on {t['transaction_date']}")
        
        conn.close()
    

        # Add retry logic for database locked errors
        max_retries = 3
        retry_count = 0
        entry_id = None
        last_error = None

        while retry_count < max_retries:
            try:
                entry_id = db.add_final_store_transaction_out(
                    transaction_date, product_code, quantity_pcs, per_piece_weight, 
                    destination, order_id, handle_type
                )

                # If destination is Dispatch, add a shipping record
                if destination == 'Dispatch':
                    try:
                        db.add_shipping_record(transaction_date, product_code, quantity_pcs, entry_id, order_id)
                    except Exception as e:
                        print(f"Warning: Failed to add shipping record: {e}")
                        # Continue even if shipping record fails - we don't want to roll back the transaction

                break  # Success, exit the loop
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e):
                    retry_count += 1
                    print(f"Database locked, retrying ({retry_count}/{max_retries})...")
                    time.sleep(1)  # Wait a bit before retrying
                    last_error = e
                else:
                    # Re-raise if it's a different operational error
                    print(f"Operational error for product {product_code}: {e}")
                    return jsonify({
                        'status': 'error',
                        'message': f'Database operation failed: {str(e)}'
                    }), 500
            except sqlite3.IntegrityError as e:
                error_msg = str(e)
                if "FOREIGN KEY constraint failed" in error_msg:
                    # Handle foreign key constraint failure
                    print(f"Foreign key constraint failed for product {product_code}: {e}")
                    return jsonify({
                        'status': 'error',
                        'message': f'Product code {product_code} is not registered in the system. Please add it first.'
                    }), 400
                else:
                    # Re-raise if it's a different integrity error
                    print(f"Integrity error for product {product_code}: {e}")
                    return jsonify({
                        'status': 'error',
                        'message': f'Database integrity error: {error_msg}'
                    }), 400
            except Exception as e:
                # Handle any other exceptions
                print(f"Error in add_final_store_out_api for product {product_code}: {e}")
                return jsonify({
                    'status': 'error',
                    'message': f'An error occurred: {str(e)}'
                }), 500

        # If we've exhausted retries and still have an error
        if entry_id is None and last_error is not None:
            return jsonify({
                'status': 'error',
                'message': f'Database is locked after {max_retries} retries. Please try again later.'
            }), 503  # Service Unavailable

        return jsonify({
            'status': 'success',
            'entry_id': entry_id,
            'message': 'Final store OUT transaction added successfully'
        }), 201
        
    except ValueError as e:
        # Handle data type conversion errors
        return jsonify({
            'status': 'error',
            'message': f'Invalid data format: {str(e)}'
        }), 400
    except Exception as e:
        # Generic error handler
        print(f"Unexpected error in add_final_store_out_api: {e}")
        return jsonify({
            'status': 'error',
            'message': f'An unexpected error occurred: {str(e)}'
        }), 500

#-------------------------------------------------------
# Plating Outward Routes
#-------------------------------------------------------

@manufacturing.route('/plating_outward')
def serve_plating_outward_page():
    """Serve the plating outward page."""
    return render_template('manufacturing/plating_outward.html')

@manufacturing.route('/api/plating_outward', methods=['GET'])
def get_plating_outward_transactions():
    """API endpoint to retrieve plating outward transactions."""
    try:
        limit = request.args.get('limit', 100, type=int)
        entries = db.get_plating_outward_transactions(limit)
        return jsonify([dict_factory(entry) for entry in entries])
    except Exception as e:
        print(f"Error in plating_outward API: {str(e)}")
        # Return empty array as fallback during development
        return jsonify([])

@manufacturing.route('/api/plating_outward', methods=['POST'])
def add_plating_outward_api():
    """API endpoint to add a new plating outward transaction."""
    data = request.json

    if not all(k in data for k in ['outward_date', 'product_code', 'quantity_pcs', 'job_worker', 'finish']):
        abort(400, description="Missing required fields (outward_date, product_code, quantity_pcs, job_worker, finish)")

    outward_date = parse_date(data['outward_date'])
    product_code = data['product_code']
    quantity_pcs = int(data['quantity_pcs'])
    job_worker = data['job_worker']
    finish = data['finish']

    if job_worker not in ['Aagna', 'Pratik Metal', 'Vijay Enterprise']:
        abort(400, description="Invalid job worker specified")

    # Pass null for final_store_transaction_id
    entry_id = db.add_plating_outward(
        outward_date, None, product_code, quantity_pcs, job_worker, finish
    )

    return jsonify({
        'status': 'success',
        'entry_id': entry_id,
        'message': 'Plating outward transaction added successfully'
    }), 201

#-------------------------------------------------------
# Assembly Receipt Routes
#-------------------------------------------------------

@manufacturing.route('/assembly_receipt')
def serve_assembly_receipt_page():
    """Serve the assembly receipt page."""
    return render_template('manufacturing/assembly_receipt.html')

@manufacturing.route('/api/assembly_receipt', methods=['GET'])
def get_assembly_receipts():
    """API endpoint to retrieve assembly receipts."""
    try:
        limit = request.args.get('limit', 100, type=int)
        entries = db.get_assembly_receipts(limit)
        return jsonify([dict_factory(entry) for entry in entries])
    except Exception as e:
        print(f"Error in assembly_receipt API: {str(e)}")
        # Return empty array as fallback during development
        return jsonify([])

@manufacturing.route('/api/assembly_receipt', methods=['POST'])
def add_assembly_receipt_api():
    """API endpoint to add a new assembly receipt."""
    data = request.json

    if not all(k in data for k in ['receipt_date', 'product_code', 'finish', 'quantity_pcs']):
        abort(400, description="Missing required fields (receipt_date, product_code, finish, quantity_pcs)")

    receipt_date = parse_date(data['receipt_date'])
    product_code = data['product_code']
    finish = data['finish']
    quantity_pcs = int(data['quantity_pcs'])
    vendor = data.get('vendor')  # Optional field, can be None

    # Validate vendor if provided
    if vendor and vendor not in ['Aagna', 'Pratik Metal', 'Vraj Brass', 'Vijay Enterprise']:
        abort(400, description="Invalid vendor. Must be one of: Aagna, Pratik Metal, Vraj Brass, Vijay Enterprise")

    try:
        # Pass null for plating_outward_id
        entry_id = db.add_assembly_receipt(
            receipt_date, None, product_code, finish, quantity_pcs, vendor
        )

        return jsonify({
            'status': 'success',
            'entry_id': entry_id,
            'message': 'Assembly receipt added successfully'
        }), 201
    except ValueError as e:
        abort(400, description=str(e))
    except Exception as e:
        print(f"Error in add_assembly_receipt_api: {str(e)}")
        abort(500, description=f"An error occurred: {str(e)}")

#-------------------------------------------------------
# Finishes CRUD Routes (NEW)
#-------------------------------------------------------
@manufacturing.route('/api/finishes', methods=['GET'])
def get_finishes_api():
    """API endpoint to retrieve all finishes."""
    try:
        finishes = db.get_finishes()
        # Convert Row objects to simple dicts for JSON serialization
        return jsonify([dict(row) for row in finishes])
    except Exception as e:
        print(f"Error in GET /api/finishes: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Failed to retrieve finishes'}), 500

@manufacturing.route('/api/finishes', methods=['POST'])
def add_finish_api():
    """API endpoint to add a new finish."""
    data = request.json
    if not data or 'finish_name' not in data or 'finish_prefix' not in data:
        abort(400, description="Missing required fields: finish_name, finish_prefix")

    finish_name = data['finish_name']
    finish_prefix = data['finish_prefix']

    try:
        finish_id = db.add_finish(finish_name, finish_prefix)
        if finish_id:
            return jsonify({'status': 'success', 'finish_id': finish_id, 'message': 'Finish added successfully'}), 201
        else:
            # Handle case where add_finish failed (e.g., unique constraint)
            return jsonify({'status': 'error', 'message': 'Failed to add finish, possibly duplicate name or prefix'}), 409 # 409 Conflict
    except Exception as e:
        print(f"Error in POST /api/finishes: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Internal server error adding finish'}), 500

@manufacturing.route('/api/finishes/<int:finish_id>', methods=['DELETE'])
def delete_finish_api(finish_id):
    """API endpoint to delete a finish."""
    try:
        deleted = db.delete_finish(finish_id)
        if deleted:
            return jsonify({'status': 'success', 'message': f'Finish ID {finish_id} deleted successfully'}), 200
        else:
            # Could be not found or failed due to FK constraint
            return jsonify({'status': 'error', 'message': f'Failed to delete finish ID {finish_id}. It might not exist or is still in use.'}), 400
    except Exception as e:
        print(f"Error in DELETE /api/finishes/{finish_id}: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Internal server error deleting finish'}), 500

#-------------------------------------------------------
# Brands CRUD Routes (NEW)
#-------------------------------------------------------
@manufacturing.route('/api/brands', methods=['GET'])
def get_brands_api():
    """API endpoint to retrieve all brands."""
    try:
        brands = db.get_brands()
        # Convert to dict and include client_id
        return jsonify([dict(row) for row in brands])
    except Exception as e:
        print(f"Error in GET /api/brands: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Failed to retrieve brands'}), 500

@manufacturing.route('/api/brands', methods=['POST'])
def add_brand_api():
    """API endpoint to add a new brand with company name and client_id."""
    data = request.json
    if not data or 'brand_name' not in data:
        abort(400, description="Missing required field: brand_name")

    brand_name = data['brand_name']
    company_name = data.get('company_name_of_brand_name')
    client_id = data.get('client_id')

    try:
        brand_id = db.add_brand(brand_name, company_name, client_id)
        if brand_id:
            return jsonify({
                'status': 'success',
                'brand_id': brand_id,
                'message': 'Brand added successfully',
                'brand_name': brand_name,
                'company_name_of_brand_name': company_name,
                'client_id': client_id
            }), 201
        else:
            return jsonify({'status': 'error', 'message': 'Failed to add brand, possibly duplicate name'}), 409 # Conflict
    except Exception as e:
        print(f"Error in POST /api/brands: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Internal server error adding brand'}), 500

@manufacturing.route('/api/brands/<int:brand_id>', methods=['DELETE'])
def delete_brand_api(brand_id):
    """API endpoint to delete a brand."""
    try:
        deleted = db.delete_brand(brand_id)
        if deleted:
            return jsonify({'status': 'success', 'message': f'Brand ID {brand_id} deleted successfully'}), 200
        else:
            # Not found or failed due to FK constraint
            return jsonify({'status': 'error', 'message': f'Failed to delete brand ID {brand_id}. It might not exist or is still in use.'}), 400
    except Exception as e:
        print(f"Error in DELETE /api/brands/{brand_id}: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Internal server error deleting brand'}), 500

#-------------------------------------------------------
# Product Routes (MODIFIED)
#-------------------------------------------------------

@manufacturing.route('/products')
def serve_products_page():
    """Serve the (new) products page."""
    return render_template('manufacturing/products.html')

@manufacturing.route('/uploads/products/<filename>')
def serve_product_photo(filename):
    """Serve uploaded product photos."""
    # Make sure the path traversal is safe, send_from_directory helps
    # Use current_app.config['UPLOAD_FOLDER'] if configured centrally
    # For now, using the path relative to the app root.
    # Construct the absolute path or path relative to app root for send_from_directory
    # Assuming UPLOAD_FOLDER = 'uploads/products' is relative to app root

    # Security: Validate filename if needed beyond secure_filename,
    # although send_from_directory provides some protection.

    try:
        # send_from_directory expects directory relative to app root or absolute path
        # Let's ensure UPLOAD_FOLDER is treated as relative to app root
        # The directory is 'uploads/products'
        return send_from_directory(UPLOAD_FOLDER, filename)
    except FileNotFoundError:
        abort(404, description="Image not found.")
    except Exception as e:
         print(f"Error serving file {filename} from {UPLOAD_FOLDER}: {e}")
         abort(500)

@manufacturing.route('/api/products', methods=['GET'])
def get_products_api():
    """API endpoint to retrieve all internal products with details."""
    try:
        products = db.get_products()
        # get_products already returns dicts with available_finishes included
        return jsonify(products)
    except Exception as e:
        print(f"Error in GET /api/products: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Failed to retrieve products'}), 500

@manufacturing.route('/api/all_products', methods=['GET'])
def get_all_products_with_brands_api():
    """API endpoint to retrieve all products with their brand information if available."""
    try:
        products = db.get_all_products_with_brands()
        return jsonify(products)
    except Exception as e:
        print(f"Error in GET /api/all_products: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Failed to retrieve all products with brands'}), 500

@manufacturing.route('/api/products/<product_code>', methods=['GET'])
def get_product_api(product_code):
    """API endpoint to retrieve a specific internal product."""
    try:
        product = db.get_product(product_code)
        if product:
            # get_product returns a dict with available_finishes included
            return jsonify(product)
        else:
            abort(404, description=f"Product with code {product_code} not found")
    except Exception as e:
        print(f"Error in GET /api/products/{product_code}: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Failed to retrieve product'}), 500

# Combined Add/Update route using POST for simplicity here,
# but PUT for update is more conventional REST practice.
# Using POST for add, PUT for update is better.

@manufacturing.route('/api/products', methods=['POST'])
def add_product_api():
    """API endpoint to add a new internal product."""
    try:
        # Get form fields (multipart/form-data)
        product_code = request.form.get('product_code')
        name = request.form.get('name')
        description = request.form.get('description')
        casting_weight = request.form.get('casting_weight')
        machining_weight = request.form.get('machining_weight')
        final_weight = request.form.get('final_weight')

        # Validate required fields
        if not product_code:
            return jsonify({'status': 'error', 'message': 'Missing required field: product_code'}), 400
        if not name:
            return jsonify({'status': 'error', 'message': 'Missing required field: name'}), 400
        if not casting_weight:
            return jsonify({'status': 'error', 'message': 'Missing required field: casting_weight'}), 400
        if not machining_weight:
            return jsonify({'status': 'error', 'message': 'Missing required field: machining_weight'}), 400
        if not final_weight:
            return jsonify({'status': 'error', 'message': 'Missing required field: final_weight'}), 400

        # Process weights
        try:
            casting_weight = float(casting_weight)
            machining_weight = float(machining_weight)
            final_weight = float(final_weight)
        except ValueError:
            return jsonify({'status': 'error', 'message': 'Weight fields must be valid numbers'}), 400

        # Process photo if provided
        photo_filename = None
        if 'photo' in request.files:
            file = request.files['photo']
            if file and file.filename != '':
                # Check file type
                if not allowed_file(file.filename):
                    return jsonify({'status': 'error', 'message': f"Invalid file type. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"}), 400

                # Convert to base64
                file_content = file.read()
                encoded_content = base64.b64encode(file_content).decode('utf-8')

                # Store the base64 encoded string
                photo_filename = f"base64:{encoded_content}"
                print(f"Image for {product_code} converted to base64 (length: {len(encoded_content)} chars)")

        # Save to database
        success = db.add_or_update_product(
            product_code=product_code,
            name=name,
            description=description,
            casting_weight=casting_weight,
            machining_weight=machining_weight,
            final_weight=final_weight,
            photo_filename=photo_filename
        )

        if success:
            new_product_data = db.get_product(product_code)
            return jsonify(new_product_data), 201
        else:
            return jsonify({'status': 'error', 'message': 'Failed to add product to database'}), 500

    except Exception as e:
        print(f"Error in POST /api/products: {str(e)}")
        return jsonify({'status': 'error', 'message': f'Internal server error adding product: {str(e)}'}), 500

@manufacturing.route('/api/products/<product_code>', methods=['PUT'])
def update_product_api(product_code):
    """API endpoint to update an existing internal product, handles base64 encoded photos."""

    existing_product = db.get_product(product_code)
    if not existing_product:
        abort(404, description=f"Product with code {product_code} not found for update")

    # Fetch existing product to get current photo filename for potential deletion
    # This was moved up to be accessible
    current_photo_filename = existing_product.get('photo_filename')
    new_photo_filename = None # Assume no new photo initially

    # Check for new photo upload
    if 'photo' in request.files:
        file = request.files['photo']
        if file and file.filename != '': # A file was uploaded
            try:
                # Check file type
                if not allowed_file(file.filename):
                    abort(400, description=f"Invalid file type. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}")

                # Convert to base64
                file_content = file.read()
                encoded_content = base64.b64encode(file_content).decode('utf-8')

                # Store the base64 encoded string
                new_photo_filename = f"base64:{encoded_content}"
                print(f"New image for {product_code} converted to base64 (length: {len(encoded_content)} chars)")
            except Exception as e:
                print(f"Error processing new photo during update for {product_code}: {e}")
                return jsonify({'status': 'error', 'message': 'Failed to process updated product photo'}), 500

    # Get other data from form fields
    data = request.form

    # Fields to update - get from form, default to existing value if not provided in form
    name = data.get('name', existing_product.get('name'))
    description = data.get('description', existing_product.get('description'))

    # Process weights - if provided in form, validate and convert
    try:
        casting_weight = existing_product.get('casting_weight')
        machining_weight = existing_product.get('machining_weight')
        final_weight = existing_product.get('final_weight')

        if 'casting_weight' in data:
            if not data['casting_weight']:
                abort(400, description="Casting weight cannot be empty")
            casting_weight = float(data['casting_weight'])

        if 'machining_weight' in data:
            if not data['machining_weight']:
                abort(400, description="Machining weight cannot be empty")
            machining_weight = float(data['machining_weight'])

        if 'final_weight' in data:
            if not data['final_weight']:
                abort(400, description="Final weight cannot be empty")
            final_weight = float(data['final_weight'])

        if 'name' in data and not data['name']:
            abort(400, description="Name cannot be empty if provided")

    except ValueError as e:
        abort(400, description=f"Invalid data type: {e}")

    try:
        # No need to safeguard the photo_filename anymore since it's just a string
        success = db.add_or_update_product(
            product_code=product_code,
            name=name,
            description=description,
            casting_weight=casting_weight,
            machining_weight=machining_weight,
            final_weight=final_weight,
            photo_filename=new_photo_filename if new_photo_filename else current_photo_filename
        )

        if success:
            updated_product = db.get_product(product_code)
            return jsonify(updated_product), 200
        else:
            return jsonify({'status': 'error', 'message': 'Failed to update product in database'}), 500

    except ValueError as ve:
        return jsonify({'status': 'error', 'message': str(ve)}), 400
    except Exception as e:
        print(f"Error in PUT /api/products/{product_code}: {e}")
        return jsonify({'status': 'error', 'message': 'Internal server error updating product'}), 500

@manufacturing.route('/api/products/<product_code>', methods=['DELETE'])
def delete_product_api(product_code):
    """API endpoint to delete an internal product and its associated photo."""

    # Get product details first to find the photo filename
    product_to_delete = db.get_product(product_code)
    if not product_to_delete:
         # Return success or 404? Let's return success-like (200/204) if already gone.
         # Or 404 if strict. Let's go with 404.
         abort(404, description=f"Product {product_code} not found for deletion.")

    photo_to_delete = product_to_delete.get('photo_filename')

    try:
        deleted_db = db.delete_product(product_code)

        if deleted_db:
             # Database deletion successful, now delete the photo file if it exists
             if photo_to_delete:
                 delete_product_photo(photo_to_delete) # Attempt deletion, ignore errors for now
             return jsonify({'status': 'success', 'message': f'Product {product_code} deleted successfully'}), 200
        else:
            # DB deletion failed (e.g., FK constraint)
            return jsonify({'status': 'error', 'message': f'Failed to delete product {product_code} from database. It might still be in use by a Brand SKU.'}), 400

    except Exception as e:
        print(f"Error in DELETE /api/products/{product_code}: {str(e)}")
        # If DB delete failed, we don't delete the photo
        return jsonify({'status': 'error', 'message': 'Internal server error deleting product'}), 500

#-------------------------------------------------------
# Brand Products (SKUs) CRUD Routes (NEW)
#-------------------------------------------------------

@manufacturing.route('/api/brand_products', methods=['GET'])
def get_brand_products_api():
    """API endpoint to retrieve brand SKU mappings, with optional filters."""
    # Example filters (add more as needed): ?brand_id=1, ?internal_product_code=XYZ
    filters = request.args.to_dict()
    # Convert potential string numbers to int if needed for filtering
    if 'brand_id' in filters:
        try: filters['brand_id'] = int(filters['brand_id'])
        except ValueError: abort(400, description="Invalid brand_id filter")
    if 'finish_id' in filters:
        try: filters['finish_id'] = int(filters['finish_id'])
        except ValueError: abort(400, description="Invalid finish_id filter")
    if 'sku_id' in filters: # Added filter for single SKU fetch by ID
        try: filters['sku_id'] = int(filters['sku_id'])
        except ValueError: abort(400, description="Invalid sku_id filter")

    try:
        skus = db.get_brand_products(filters)
        # get_brand_products returns list of dicts now
        return jsonify(skus)
    except Exception as e:
        print(f"Error in GET /api/brand_products: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Failed to retrieve brand products'}), 500

@manufacturing.route('/api/brand_products', methods=['POST'])
def add_brand_product_api():
    """API endpoint to add a new brand SKU mapping.
    Finish ID is now optional.
    """
    if not request.is_json:
        abort(400, description="Request must be JSON")
    data = request.get_json()

    # Updated required fields: finish_id is no longer required here
    if not all(k in data for k in ['brand_sku_code', 'brand_id', 'internal_product_code']):
        abort(400, description="Missing required fields: brand_sku_code, brand_id, internal_product_code")

    brand_sku_code = data['brand_sku_code']
    brand_id = data['brand_id']
    internal_product_code = data['internal_product_code']
    # Finish ID is optional
    finish_id = data.get('finish_id') # Will be None if not provided
    bp_description = data.get('description')
    # Auto-fetched weights
    casting_weight = data.get('casting_weight')
    machining_weight = data.get('machining_weight')
    final_weight = data.get('final_weight')

    # --- Removed logic for create_new_product - Assuming product always exists ---
    # If create_new_product is needed again, the finish_id validation part
    # within that block would need to be removed or adjusted.

    # --- Add the Brand Product mapping ---
    try:
        # Convert IDs to integers if present
        brand_id = int(brand_id)
        if finish_id is not None:
            try:
                finish_id = int(finish_id)
            except (ValueError, TypeError):
                 abort(400, description="Invalid finish_id format.")
        else:
             finish_id = None # Ensure it's None if not provided or invalid

        # Call DB function - finish_id can be None
        # Pass fetched weights and description
        sku_id = db.add_brand_product(
            brand_sku_code=brand_sku_code,
            internal_product_code=internal_product_code,
            brand_id=brand_id,
            finish_id=finish_id,
            description=bp_description,
            casting_weight=casting_weight,
            machining_weight=machining_weight,
            final_weight=final_weight
        )

        if sku_id:
            # Fetch the newly created SKU details to return
            new_sku_data_list = db.get_brand_products({'sku_id': sku_id})
            new_sku_data = new_sku_data_list[0] if new_sku_data_list else None
            if new_sku_data:
                # Add the potentially null finish_id back if needed for response consistency
                # Although get_brand_products might already handle this
                if 'finish_id' not in new_sku_data:
                    new_sku_data['finish_id'] = finish_id
                return jsonify({'status': 'success', 'brand_product': new_sku_data, 'message': 'Brand product SKU added successfully'}), 201
            else:
                 print(f"Error: SKU {sku_id} added but could not be retrieved.")
                 return jsonify({'status': 'error', 'message': 'SKU added but failed to retrieve details.'}), 500
        else:
            # Error handled within add_brand_product (e.g., validation, duplicate Brand+SKU)
            # Update message as finish ID is no longer a required validation point here
            return jsonify({'status': 'error', 'message': 'Failed to add brand product SKU. Possible reason: Duplicate Brand+SKU code.'}), 400

    except ValueError as ve:
        # Catch validation errors from integer conversion or potentially db helper
        return jsonify({'status': 'error', 'message': str(ve)}), 400
    except Exception as e:
        print(f"Error in POST /api/brand_products: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Internal server error adding brand product SKU'}), 500

@manufacturing.route('/api/brand_products/<int:sku_id>', methods=['PUT'])
def update_brand_product_api(sku_id):
    """API endpoint to update an existing brand SKU mapping.
       Finish ID is now optional.
    """
    if not request.is_json:
        abort(400, description="Request must be JSON")
    data = request.get_json()

    # Extract fields to update
    brand_sku_code = data.get('brand_sku_code')
    internal_product_code = data.get('internal_product_code')
    brand_id = data.get('brand_id')
    # finish_id might not be present in the request data at all
    # Use a sentinel to check if it was explicitly sent (e.g., for setting to null)
    # However, since frontend removed it, it just won't be sent. Treat absence as 'no change'.
    finish_id_provided = 'finish_id' in data
    finish_id = data.get('finish_id')
    description = data.get('description')
    casting_weight = data.get('casting_weight')
    machining_weight = data.get('machining_weight')
    final_weight = data.get('final_weight')

    # --- Type conversion for IDs if provided --- #
    try:
        if brand_id is not None: brand_id = int(brand_id)
        if finish_id_provided:
             if finish_id is not None:
                 finish_id = int(finish_id)
             else:
                 finish_id = None # Allow explicitly setting to NULL

    except (ValueError, TypeError):
         abort(400, description="Invalid brand_id or finish_id format.")

    # We need to tell the DB function *not* to update finish_id if it wasn't provided
    update_fields = {
        'brand_sku_code': brand_sku_code,
        'internal_product_code': internal_product_code,
        'brand_id': brand_id,
        'description': description,
        'casting_weight': casting_weight,
        'machining_weight': machining_weight,
        'final_weight': final_weight
    }
    if finish_id_provided:
        update_fields['finish_id'] = finish_id

    # Remove keys where value is None, unless it's finish_id (explicit null)
    # or description (allow setting description to empty/null)
    update_payload = {k: v for k, v in update_fields.items()
                       if v is not None or k == 'finish_id' or k == 'description'}

    if not update_payload: # Nothing to update
        return jsonify({'status': 'info', 'message': 'No fields provided to update.'}), 200

    try:
        # The db.update_brand_product needs modification to accept kwargs or handle None
        success = db.update_brand_product(sku_id=sku_id, **update_payload)

        if success:
            # Fetch updated data to return
            updated_sku_data_list = db.get_brand_products({'sku_id': sku_id})
            updated_sku_data = updated_sku_data_list[0] if updated_sku_data_list else None
            if updated_sku_data:
                 return jsonify({'status': 'success', 'brand_product': updated_sku_data, 'message': 'Brand product SKU updated successfully'}), 200
            else:
                print(f"Error: SKU {sku_id} updated but could not be retrieved.")
                # If update succeeded but retrieval failed, maybe 500 is better
                return jsonify({'status': 'error', 'message': 'SKU updated but failed to retrieve details.'}), 500
        else:
             # Update helper returns False if not found or error occurred
             # Check if it exists to return 404 vs 400/500
             if not db.get_brand_products({'sku_id': sku_id}):
                  return jsonify({'status': 'error', 'message': f'Brand product SKU {sku_id} not found.'}), 404
             else:
                  # Update message - finish ID less likely the cause now
                  return jsonify({'status': 'error', 'message': f'Failed to update brand product SKU {sku_id}. Possible reason: Duplicate Brand+SKU code.'}), 400

    except ValueError as ve: # Catch validation errors from helper
         return jsonify({'status': 'error', 'message': str(ve)}), 400
    except Exception as e:
        print(f"Error in PUT /api/brand_products/{sku_id}: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Internal server error updating brand product SKU'}), 500

@manufacturing.route('/api/brand_products/<int:sku_id>', methods=['DELETE'])
def delete_brand_product_api(sku_id):
    """API endpoint to delete a brand SKU mapping."""
    try:
        deleted = db.delete_brand_product(sku_id)
        if deleted:
            return jsonify({'status': 'success', 'message': f'Brand product SKU {sku_id} deleted successfully'}), 200
        else:
            return jsonify({'status': 'error', 'message': f'Failed to delete brand product SKU {sku_id}. It might not exist.'}), 404
    except Exception as e:
        print(f"Error in DELETE /api/brand_products/{sku_id}: {str(e)}")
        return jsonify({'status': 'error', 'message': 'Internal server error deleting brand product SKU'}), 500

#-------------------------------------------------------
# WIP & Inventory API Routes
#-------------------------------------------------------

@manufacturing.route('/api/wip/cnc', methods=['GET'])
def get_cnc_wip_api():
    wip = db.calculate_cnc_wip()
    return jsonify(wip)

@manufacturing.route('/api/stock/primary', methods=['GET'])
def get_primary_stock_api():
    stock = db.calculate_primary_store_stock()
    return jsonify(stock)

@manufacturing.route('/api/wip/buffing', methods=['GET'])
def get_buffing_wip_api():
    wip = db.calculate_buffing_wip()
    return jsonify(wip)

@manufacturing.route('/api/wip/vraj', methods=['GET'])
def get_vraj_wip_api():
    try:
        wip = db.calculate_vraj_wip()
        return jsonify(wip)
    except Exception as e:
        print(f"Error in Vraj WIP API: {str(e)}")
        # Return empty object as fallback during development
        return jsonify({})

@manufacturing.route('/api/stock/final', methods=['GET'])
def get_final_stock_api():
    """API endpoint to retrieve final store stock with handle type information."""
    try:
        # Debug specific product codes with issues
        problematic_codes = ["110(B)"]
        for code in problematic_codes:
            # Log raw transaction data for debugging
            conn = db.get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT transaction_type, quantity_pcs, handle_type, transaction_date
                FROM final_store_transaction
                WHERE product_code = ?
                ORDER BY transaction_date
            ''', (code,))
            
            transactions = cursor.fetchall()
            print(f"DEBUG: Found {len(transactions)} transactions for product '{code}'")
            
            in_qty = 0
            out_qty = 0
            
            for t in transactions:
                if t['transaction_type'] == 'IN':
                    in_qty += t['quantity_pcs']
                else:
                    out_qty += t['quantity_pcs']
                    
                print(f"DEBUG: {t['transaction_date']} - {t['transaction_type']} - {t['handle_type']} - {t['quantity_pcs']} pcs")
            
            # Get current stock from the new stock table
            cursor.execute('''
                SELECT product_code, handle_type, quantity_pcs, last_updated
                FROM final_store_stock
                WHERE product_code = ?
            ''', (code,))
            
            stock_records = cursor.fetchall()
            print(f"DEBUG: Found {len(stock_records)} stock records for product '{code}'")
            
            for sr in stock_records:
                print(f"DEBUG: Stock table record for '{code}' ({sr['handle_type']}): {sr['quantity_pcs']} pcs (updated: {sr['last_updated']})")
            
            conn.close()
        
        # Get all stock from the final_store_stock table
        stock_by_handle = db.get_final_store_stock()
    
    except Exception as e:
        print(f"ERROR in get_final_stock_api: {e}")
        stock_by_handle = {}
    
    # Transform the nested structure to a flattened format for the frontend
    flattened_stock = {}
    
    for product_code, handle_types in stock_by_handle.items():
        for handle_type, quantity in handle_types.items():
            # Create a unique key that is safe regardless of product code contents
            key = f"{len(flattened_stock)}"  # Simple numeric key to avoid issues with special chars
            
            flattened_stock[key] = {
                "product_code": product_code,
                "handle_type": handle_type,
                "quantity": quantity
            }
    
    return jsonify(flattened_stock)

@manufacturing.route('/api/wip/plating', methods=['GET'])
def get_plating_wip_api():
    try:
        wip = db.calculate_plating_wip()
        wip_json = {f'{k[0]}|{k[1]}|{k[2]}': v for k, v in wip.items()}
        return jsonify(wip_json)
    except Exception as e:
        print(f"Error in plating WIP API: {str(e)}")
        # Return empty object as fallback during development
        return jsonify({})

#-------------------------------------------------------
# Reporting Routes
#-------------------------------------------------------

@manufacturing.route('/reports')
def serve_reports_page():
    """Serve the main reports page."""
    return render_template('manufacturing/reports.html')

@manufacturing.route('/reports/wip')
def serve_wip_report_page():
    """Serve the WIP report page."""
    return render_template('manufacturing/reports/wip_report.html')

@manufacturing.route('/reports/inventory')
def serve_inventory_report_page():
    """Serve the inventory report page."""
    return render_template('manufacturing/reports/inventory_report.html')

@manufacturing.route('/api/reports/material_loss', methods=['GET'])
def get_material_loss_report_api():
    """API endpoint to retrieve material loss report."""
    report = db.get_material_loss_report()
    return jsonify(report)

@manufacturing.route('/api/reports/production_history/<product_code>', methods=['GET'])
def get_production_history_api(product_code):
    """API endpoint to retrieve production history for a product."""
    history = db.get_production_history(product_code)
    return jsonify([dict_factory(entry) for entry in history])

#-------------------------------------------------------
# Dashboard Route
#-------------------------------------------------------

@manufacturing.route('/dashboard')
def serve_dashboard():
    """Serve the manufacturing dashboard."""
    return render_template('manufacturing/dashboard.html')

#-------------------------------------------------------
# API Route Aliases for Backward Compatibility
#-------------------------------------------------------
@manufacturing.route('/api/casting', methods=['GET'])
def get_casting_api_alias():
    """Alias for backward compatibility."""
    try:
        limit = request.args.get('limit', 100, type=int)
        entries = db.get_casting_orders(limit)
        return jsonify([dict_factory(entry) for entry in entries])
    except Exception as e:
        print(f"Error in GET /api/casting: {str(e)}")
        return jsonify([])

@manufacturing.route('/api/casting', methods=['POST'])
def add_casting_api_alias():
    """Alias for backward compatibility."""
    return add_casting_order_api()

@manufacturing.route('/api/cnc', methods=['GET'])
def get_cnc_api_alias():
    """Alias for backward compatibility."""
    try:
        limit = request.args.get('limit', 100, type=int)
        entries = db.get_cnc_outward_transactions(limit)
        return jsonify([dict_factory(entry) for entry in entries])
    except Exception as e:
        print(f"Error in GET /api/cnc: {str(e)}")
        return jsonify([])

@manufacturing.route('/api/cnc', methods=['POST'])
def add_cnc_api_alias():
    """Alias for backward compatibility."""
    return add_cnc_outward_api()

@manufacturing.route('/api/primary_store', methods=['GET'])
def get_primary_store_api_alias():
    """Alias for backward compatibility."""
    try:
        limit = request.args.get('limit', 100, type=int)
        entries = db.get_primary_store_receipts(limit)
        return jsonify([dict_factory(entry) for entry in entries])
    except Exception as e:
        print(f"Error in GET /api/primary_store: {str(e)}")
        return jsonify([])

@manufacturing.route('/api/primary_store', methods=['POST'])
def add_primary_store_api_alias():
    """Alias for backward compatibility."""
    try:
        return add_primary_store_receipt_api()
    except Exception as e:
        print(f"Error in POST /api/primary_store alias: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'An error occurred: {str(e)}'
        }), 500

@manufacturing.route('/api/buffing', methods=['GET'])
def get_buffing_api_alias():
    """Alias for backward compatibility. Uses the manufacturing database's buffing_outward_transaction table.
    Directly connects to the manufacturing database without using any helper functions."""
    try:
        limit = request.args.get('limit', 100, type=int)

        # Direct connection to manufacturing.db
        import sqlite3
        conn = sqlite3.connect('manufacturing.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Query the buffing_outward_transaction table directly
        cursor.execute('''
            SELECT bot.*, pm.description, bc.name as contractor_name
            FROM buffing_outward_transaction bot
            LEFT JOIN product_master pm ON bot.product_code = pm.product_code
            LEFT JOIN buffing_contractors bc ON bot.contractor_id = bc.id
            ORDER BY bot.outward_date DESC, bot.id DESC LIMIT ?
        ''', (limit,))

        entries = cursor.fetchall()
        conn.close()

        # Transform the data to match what the DataTable expects
        transformed_entries = []
        for entry in entries:
            entry_dict = dict(zip(entry.keys(), entry))
            # Rename transaction_date to outward_date if needed
            if 'transaction_date' in entry_dict and 'outward_date' not in entry_dict:
                entry_dict['outward_date'] = entry_dict['transaction_date']
            transformed_entries.append(entry_dict)

        return jsonify(transformed_entries)
    except Exception as e:
        print(f"Error in GET /api/buffing: {str(e)}")
        return jsonify([])

@manufacturing.route('/api/buffing', methods=['POST'])
def add_buffing_api_alias():
    """Alias for backward compatibility. Uses the manufacturing database's buffing_outward_transaction table.
    Directly connects to the manufacturing database without using any helper functions."""
    try:
        if not request.json:
            return jsonify({'status': 'error', 'message': 'No JSON data provided'}), 400

        data = request.json.copy() if hasattr(request.json, 'copy') else dict(request.json)

        # Map transaction_date to outward_date if needed
        if 'transaction_date' in data and 'outward_date' not in data:
            data['outward_date'] = data['transaction_date']

        # Extract required fields
        if not all(k in data for k in ['outward_date', 'product_code', 'quantity_pcs', 'per_piece_weight']):
            return jsonify({'status': 'error', 'message': 'Missing required fields (outward_date, product_code, quantity_pcs, per_piece_weight)'}), 400

        outward_date = parse_date(data['outward_date'])
        product_code = data['product_code']
        quantity_pcs = int(data['quantity_pcs'])
        per_piece_weight = float(data['per_piece_weight'])
        primary_store_receipt_id = data.get('primary_store_receipt_id')
        contractor_id = data.get('contractor_id')

        # Direct connection to manufacturing.db
        import sqlite3
        conn = sqlite3.connect('manufacturing.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Check if product exists, if not, create it with default values
        cursor.execute("SELECT 1 FROM product_master WHERE product_code = ?", (product_code,))
        if not cursor.fetchone():
            # Create a new product with default values
            description = f"Auto-created for buffing outward"
            cursor.execute('''
                INSERT INTO product_master (product_code, description, casting_weight, machining_weight, final_weight)
                VALUES (?, ?, 0, 0, 0)
            ''', (product_code, description))
            print(f"Auto-created product {product_code} with default values for buffing outward")

        # Insert the buffing outward transaction
        cursor.execute('''
            INSERT INTO buffing_outward_transaction
            (outward_date, product_code, primary_store_receipt_id, quantity_pcs, per_piece_weight, contractor_id)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (outward_date, product_code, primary_store_receipt_id, quantity_pcs, per_piece_weight, contractor_id))

        conn.commit()
        entry_id = cursor.lastrowid
        conn.close()

        return jsonify({
            'status': 'success',
            'entry_id': entry_id,
            'message': 'Buffing outward transaction added successfully'
        }), 201
    except Exception as e:
        print(f"Error in POST /api/buffing: {str(e)}")
        return jsonify({'status': 'error', 'message': f'An error occurred: {str(e)}'}), 500

@manufacturing.route('/api/products/<product_code>/photo', methods=['POST'])
def upload_product_photo_api(product_code):
    """API endpoint to upload a photo for a specific product."""
    try:
        # Check if the product exists
        product = db.get_product(product_code)
        if not product:
            # Try to get the product from the main database
            main_conn = db.get_main_db_connection()
            try:
                main_cursor = main_conn.cursor()
                main_cursor.execute("SELECT 1 FROM product_master WHERE product_code = ?", (product_code,))
                product_exists = main_cursor.fetchone() is not None
            except Exception as e:
                print(f"Error checking main database for product {product_code}: {e}")
                product_exists = False
            finally:
                main_conn.close()

            if not product_exists:
                abort(404, description=f"Product with code {product_code} not found")

        # Check if a file was uploaded
        if 'photo' not in request.files:
            abort(400, description="No photo file provided")

        file = request.files['photo']
        if file.filename == '':
            abort(400, description="No photo file selected")

        # Check file type
        if not allowed_file(file.filename):
            abort(400, description=f"Invalid file type. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}")

        # Convert to base64
        file_content = file.read()
        encoded_content = base64.b64encode(file_content).decode('utf-8')

        # Store the base64 encoded string
        photo_filename = f"base64:{encoded_content}"
        print(f"Image for {product_code} converted to base64 (length: {len(encoded_content)} chars)")

        # Update the product in the manufacturing database
        success = db.add_or_update_product(
            product_code=product_code,
            photo_filename=photo_filename
        )

        # Also update the product in the main database if it exists there
        main_conn = db.get_main_db_connection()
        try:
            main_cursor = main_conn.cursor()
            main_cursor.execute("UPDATE product_master SET photo_filename = ? WHERE product_code = ?",
                               (photo_filename, product_code))
            main_conn.commit()
        except Exception as e:
            print(f"Error updating photo in main database for product {product_code}: {e}")
        finally:
            main_conn.close()

        if success:
            return jsonify({
                'status': 'success',
                'message': f'Photo uploaded successfully for product {product_code}',
                'product_code': product_code
            }), 200
        else:
            return jsonify({'status': 'error', 'message': 'Failed to update product photo in database'}), 500

    except Exception as e:
        print(f"Error in POST /api/products/{product_code}/photo: {str(e)}")
        return jsonify({'status': 'error', 'message': f'Internal server error uploading product photo: {str(e)}'}), 500

@manufacturing.route('/api/products/<product_code>/photo')
def get_product_photo_api(product_code):
    """API endpoint to serve a product photo directly."""
    try:
        # Get the product to find the photo filename
        product = db.get_product(product_code)
        if not product or not product.get('photo_filename'):
            # Try to get the product from the main database
            main_conn = db.get_main_db_connection()
            try:
                main_cursor = main_conn.cursor()
                main_cursor.execute("SELECT photo_filename FROM product_master WHERE product_code = ?", (product_code,))
                main_product = main_cursor.fetchone()
                if main_product and main_product['photo_filename']:
                    photo_filename = main_product['photo_filename']
                else:
                    abort(404, description="Product or image not found")
            except Exception as e:
                print(f"Error checking main database for product photo {product_code}: {e}")
                abort(404, description="Product or image not found")
            finally:
                main_conn.close()
        else:
            photo_filename = product['photo_filename']

        # Check if it's a base64 encoded image
        if photo_filename.startswith('base64:'):
            # Extract the base64 content (without the prefix)
            base64_content = photo_filename[7:]  # Remove 'base64:' prefix

            # Build a data URL that can be used directly in an img tag
            content_type = 'image/jpeg'  # Default type, could be improved by storing type info

            # For direct image serving, we need to return the actual image data
            # with the appropriate content type header
            response = Response(
                base64.b64decode(base64_content),
                mimetype=content_type
            )
            return response
        else:
            # For backwards compatibility with file paths
            try:
                file_path = os.path.join(UPLOAD_FOLDER, photo_filename)
                # Check if file exists
                if not os.path.exists(file_path):
                    abort(404, description="Image file not found")

                # Serve the file directly
                return send_from_directory(UPLOAD_FOLDER, photo_filename)
            except Exception as e:
                print(f"Error serving image for {product_code}: {e}")
                abort(500, description="Failed to serve product image")
    except Exception as e:
        print(f"Error in get_product_photo_api for {product_code}: {e}")
        abort(500, description="Internal server error serving product image")

@manufacturing.route('/api/products/image/<product_code>')
def get_product_image_api(product_code):
    """API endpoint to serve a product image as base64 (either directly or from file)."""
    try:
        # Get the product to find the photo filename
        product = db.get_product(product_code)
        if not product or not product.get('photo_filename'):
            # Try to get the product from the main database
            main_conn = db.get_main_db_connection()
            try:
                main_cursor = main_conn.cursor()
                main_cursor.execute("SELECT photo_filename FROM product_master WHERE product_code = ?", (product_code,))
                main_product = main_cursor.fetchone()
                if main_product and main_product['photo_filename']:
                    photo_filename = main_product['photo_filename']
                else:
                    abort(404, description="Product or image not found")
            except Exception as e:
                print(f"Error checking main database for product photo {product_code}: {e}")
                abort(404, description="Product or image not found")
            finally:
                main_conn.close()
        else:
            photo_filename = product['photo_filename']

        # Check if it's a base64 encoded image
        if photo_filename.startswith('base64:'):
            # Extract the base64 content (without the prefix)
            base64_content = photo_filename[7:]  # Remove 'base64:' prefix

            # Build a data URL that can be used directly in an img tag
            content_type = 'image/jpeg'  # Default type, could be improved by storing type info
            response_data = {
                'product_code': product_code,
                'image_data_url': f"data:{content_type};base64,{base64_content}"
            }
            return jsonify(response_data)

        else:
            # For backwards compatibility with file paths
            try:
                file_path = os.path.join(UPLOAD_FOLDER, photo_filename)
                # Check if file exists
                if not os.path.exists(file_path):
                    abort(404, description="Image file not found")

                # Return URL to the file
                image_url = url_for('manufacturing.serve_product_image', filename=photo_filename)
                response_data = {
                    'product_code': product_code,
                    'image_url': image_url
                }
                return jsonify(response_data)
            except Exception as e:
                print(f"Error serving image for {product_code}: {e}")
                abort(500, description="Failed to serve product image")
    except Exception as e:
        print(f"Error in get_product_image_api for {product_code}: {e}")
        abort(500, description="Internal server error serving product image")

@manufacturing.route('/api/products/serve-image/<filename>')
def serve_product_image(filename):
    """Serve a product image from the upload folder."""
    try:
        return send_from_directory(UPLOAD_FOLDER, filename)
    except FileNotFoundError:
        abort(404, description="Image not found.")
    except Exception as e:
        print(f"Error serving file {filename} from {UPLOAD_FOLDER}: {e}")
        abort(500)

@manufacturing.route('/api/final_store/transactions', methods=['GET'])
def get_final_store_transactions_api():
    print("--- ENTERING get_final_store_transactions_api ---") # Log entry
    conn = None # Initialize conn to None
    try:
        limit = request.args.get('limit', 100, type=int) # Keep limit for potential future use but ignore in query for now
        print(f"Attempting to get DB connection via db.get_db_connection()")

        # Use the connection method from the db module
        conn = db.get_db_connection()
        if conn:
            print(f"DB connection obtained successfully: {conn}")
        else:
            print("ERROR: Failed to get DB connection from db.get_db_connection()")
            # Abort or return error if connection failed
            return jsonify({"error": "Failed to connect to database"}), 500

        # The db module's connection likely already sets row_factory
        cursor = conn.cursor()
        print(f"Cursor created: {cursor}")

        # Simplified SQL query for debugging
        query = 'SELECT * FROM final_store_transaction'
        print(f"DEBUG: Preparing to execute simplified query: {query}")

        cursor.execute(query)
        print(f"DEBUG: Query executed successfully.")

        entries = cursor.fetchall() # Fetch all rows
        print(f"DEBUG: Fetched {len(entries)} raw rows.")

        # Debug: Print first raw row if exists
        if entries:
            print(f"DEBUG: First raw row: {entries[0]}")

        # Convert rows to dictionaries using dict_factory
        print("DEBUG: Attempting dict_factory conversion...")
        result = [dict_factory(entry) for entry in entries]
        print(f"DEBUG: Conversion complete. Result length: {len(result)}")

        print(f"--- RETURNING {len(result)} final store transactions ---")
        return jsonify(result)

    except sqlite3.Error as sql_err: # Catch SQLite specific errors
        print(f"!!! SQLite ERROR in get_final_store_transactions_api: {sql_err}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Database error: {sql_err}"}), 500
    except Exception as e:
        print(f"!!! GENERAL ERROR in get_final_store_transactions_api: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500
    finally:
        # Ensure the connection is closed if it was opened
        if conn:
            print(f"Closing DB connection: {conn}")
            conn.close()
        else:
            print("No DB connection to close.")
        print("--- EXITING get_final_store_transactions_api ---")

def register_manufacturing_blueprint(app):
    """Register the manufacturing blueprint with the Flask app."""
    app.register_blueprint(manufacturing)