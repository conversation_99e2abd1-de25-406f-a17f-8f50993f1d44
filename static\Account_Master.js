"use strict";

      // Constants
      const CLIENT_DATA_URL = "/get_client_data";
      const COPY_SUCCESS_DURATION = 2000; // milliseconds

      // Function to fetch client data
      async function fetchClientData() {
        showLoadingIndicator();
        try {
          const response = await fetch(CLIENT_DATA_URL);
          if (!response.ok) {
            throw new Error(
              `Failed to fetch client data: ${response.status} ${response.statusText}`
            );
          }
          const clients = await response.json();
          hideLoadingIndicator();
          return clients;
        } catch (error) {
          hideLoadingIndicator();
          console.error("Error fetching client data:", error);
          displayErrorMessage("Failed to load client data.");
          return [];
        }
      }

      function createClientCard(client) {
        const gstContent = client.TransportID || "";
        const gstSection = gstContent
          ? `
          <p class="client-info"><strong>GST:</strong> ${gstContent}
            <button class="copy-btn" aria-label="Copy GST" onclick="copyToClipboard('${gstContent}')">Copy</button>
          </p>
        `
          : "";

        // Prepare a copy button for PARTY_GST if available
        const partyGstContent = client.PARTYGST || "";
        const partyGstSection = partyGstContent
          ? `
          <p class="client-info"><strong>GST:</strong> ${partyGstContent}
            <button class="copy-btn" aria-label="Copy Party GST" onclick="copyToClipboard('${partyGstContent}')">Copy</button>
          </p>
        `
          : `<p class="client-info"><strong>GST:</strong></p>`;

        return `
          <div class="client-card">
            <div class="card-content">
              <div class="section">
                <h3 class="section-title">Client Details</h3>
                <p class="client-info"><strong>Client ID:</strong> ${client.client_Id || ""}</p>
                <p class="client-info"><strong>Name:</strong> ${client.party_Name || ""}</p>
                ${partyGstSection}
                <p class="client-info"><strong>Mobile:</strong> ${client.Party_M_O || ""}</p>
                <p class="client-info"><strong>City:</strong> ${client.Booking || ""}</p>
              </div>
              <div class="section">
                <h3 class="section-title">Business Information</h3>
                <span class="badge">${client.Discount || 0}% Discount</span>
                <p class="client-info"><strong>Brand Name:</strong> ${client.Logo || ""}</p>
                <p class="client-info"><strong>Bag Type:</strong> ${client.BAG || ""}</p>
                <p class="client-info"><strong>Box Type:</strong> ${client.BOX || ""}</p>
                ${
                  client.Notes || client.Notes_2
                    ? `
                  <div class="notes">
                    ${client.Notes ? `<p class="client-info"><strong>Notes:</strong> ${client.Notes}</p>` : ""}
                    ${
                      client.Notes_2
                        ? `<p class="client-info"><strong>Additional Notes:</strong> ${client.Notes_2}</p>`
                        : ""
                    }
                  </div>
                `
                    : ""
                }
              </div>
              <div class="section">
                <h3 class="section-title">Transport Details</h3>
                <p class="client-info"><strong>Booking:</strong> ${client.Booking || ""}</p>
                <p class="client-info"><strong>Name:</strong> ${client.Transport || ""}</p>
                <p class="client-info"><strong>Mobile:</strong> ${client.Transport_M_O || ""}</p>
                ${gstSection}
                ${
                  client.Additional__Transport || client.Additional__Transport_I_D || client.E_way_bill__Pincode
                    ? `
                  <div class="notes">
                    ${
                      client.Additional__Transport
                        ? `<p class="client-info"><strong>Additional Transport:</strong> ${client.Additional__Transport}</p>`
                        : ""
                    }
                    ${
                      client.Additional__Transport_I_D
                        ? `<p class="client-info"><strong>Additional Transport ID:</strong> ${client.Additional__Transport_I_D}</p>`
                        : ""
                    }
                    ${
                      client.E_way_bill__Pincode
                        ? `<p class="client-info"><strong>E-way Bill Pincode:</strong> ${client.E_way_bill__Pincode}</p>`
                        : ""
                    }
                  </div>
                `
                    : ""
                }
              </div>
            </div>
          </div>
        `;
      }

      // Function to copy text to clipboard
      function copyToClipboard(text) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard
            .writeText(text)
            .then(() => showCopySuccess())
            .catch((err) => showCopyError("Failed to copy: " + err));
        } else {
          // Fallback for older browsers
          const textArea = document.createElement("textarea");
          textArea.value = text;
          document.body.appendChild(textArea);
          textArea.select();
          try {
            document.execCommand("copy");
            showCopySuccess();
          } catch (err) {
            showCopyError("Failed to copy: " + err);
          }
          document.body.removeChild(textArea);
        }
      }

      // Function to show a temporary copy success message
      function showCopySuccess() {
        showTemporaryMessage("GST Copied!", "copy-success");
      }

      // Function to show a temporary copy error message
      function showCopyError(message) {
        showTemporaryMessage(message, "copy-error");
      }

      // Function to show a temporary message
      function showTemporaryMessage(message, className) {
        const messageDiv = document.createElement("div");
        messageDiv.className = className;
        messageDiv.textContent = message;
        document.body.appendChild(messageDiv);
        setTimeout(() => {
          messageDiv.remove();
        }, COPY_SUCCESS_DURATION);
      }

      // Render client cards
      function renderClientCards(clients) {
        const clientList = document.getElementById("clientList");
        const noResultsDiv = document.getElementById("noResults");

        if (clients.length === 0) {
          clientList.innerHTML = "";
          noResultsDiv.style.display = "block";
        } else {
          noResultsDiv.style.display = "none";
          clientList.innerHTML = clients
            .map((client) => createClientCard(client))
            .join("");
        }
      }

      // Filter clients based on search input
      function filterClients(clients, searchTerm) {
        const term = searchTerm.toLowerCase().trim();
        return clients.filter((client) => {
          const clientId = (client.client_Id || "").toLowerCase();
          const name = (client.Party_Name || "").toLowerCase();
          const brand = (client.Logo || "").toLowerCase();
          const transport = (client.Transport || "").toLowerCase();
          return (
            clientId.includes(term) ||
            name.includes(term) ||
            brand.includes(term) ||
            transport.includes(term)
          );
        });
      }

      // Debounce function
      function debounce(func, delay) {
        let timeout;
        return function (...args) {
          const context = this;
          clearTimeout(timeout);
          timeout = setTimeout(() => func.apply(context, args), delay);
        };
      }

      // Show loading indicator
      function showLoadingIndicator() {
        document.getElementById("loadingIndicator").style.display = "flex";
      }

      // Hide loading indicator
      function hideLoadingIndicator() {
        document.getElementById("loadingIndicator").style.display = "none";
      }

      // Display an error message
      function displayErrorMessage(message) {
        showCopyError(message);
      }

      // Initialize page: fetch data and set up event listeners
      async function initializePage() {
        const clients = await fetchClientData();
        renderClientCards(clients);

        const searchInput = document.querySelector(".search-bar");
        const debouncedSearch = debounce((e) => {
          const filteredClients = filterClients(clients, e.target.value);
          renderClientCards(filteredClients);
        }, 250);

        searchInput.addEventListener("input", debouncedSearch);
      }

      // Start the application
      initializePage();